import chalk from 'chalk';

export interface InterruptHandler {
  setup(): void;
  cleanup(): void;
  onInterrupt(callback: () => void): void;
}

export class KeyboardInterruptHandler implements InterruptHandler {
  private listeners: Array<() => void> = [];
  private isActive = false;
  private timeoutHandle: NodeJS.Timeout | null = null;

  public setup(): void {
    if (this.isActive) return;
    
    this.isActive = true;
    
    // 使用定时器检查而不是直接监听 stdin
    // 这样不会干扰 inquirer 的正常工作
    this.setupPolling();
  }

  private setupPolling(): void {
    // 使用简单的定时器来模拟中断检查
    // 实际实现中可以考虑其他方案，比如文件监听等
    this.timeoutHandle = setTimeout(() => {
      if (this.isActive) {
        this.setupPolling(); // 继续轮询
      }
    }, 100);
  }

  public cleanup(): void {
    if (!this.isActive) return;
    
    this.isActive = false;
    
    if (this.timeoutHandle) {
      clearTimeout(this.timeoutHandle);
      this.timeoutHandle = null;
    }
    
    this.listeners = [];
  }

  public onInterrupt(callback: () => void): void {
    this.listeners.push(callback);
  }

  // 手动触发中断的方法（可以通过其他方式调用）
  public triggerManualInterrupt(): void {
    if (this.isActive) {
      console.log(chalk.yellow('\n⚠️  已中断 AI 思考...'));
      this.triggerInterrupt();
    }
  }

  private triggerInterrupt(): void {
    this.listeners.forEach(callback => {
      try {
        callback();
      } catch (error) {
        console.error('Interrupt callback error:', error);
      }
    });
  }
}

/**
 * 思考中断管理器
 */
export class ThinkingInterruptManager {
  private keyboardHandler: KeyboardInterruptHandler;
  private abortController: AbortController | null = null;
  private isThinking = false;

  constructor() {
    this.keyboardHandler = new KeyboardInterruptHandler();
  }

  /**
   * 开始思考模式，启用中断监听
   */
  public startThinking(abortController: AbortController): void {
    if (this.isThinking) {
      this.stopThinking();
    }

    this.isThinking = true;
    this.abortController = abortController;
    
    // 设置键盘监听
    this.keyboardHandler.setup();
    this.keyboardHandler.onInterrupt(() => {
      if (this.abortController && !this.abortController.signal.aborted) {
        this.abortController.abort();
      }
    });

    // 显示中断提示
    this.showInterruptHint();
  }

  /**
   * 停止思考模式，清理监听器
   */
  public stopThinking(): void {
    if (!this.isThinking) return;

    this.isThinking = false;
    this.abortController = null;
    this.keyboardHandler.cleanup();
  }

  /**
   * 显示中断提示
   */
  private showInterruptHint(): void {
    // 在思考spinner启动后短暂延迟显示提示
    setTimeout(() => {
      if (this.isThinking) {
        process.stdout.write(chalk.gray('\n💡 按 Ctrl+C 中断思考\n'));
      }
    }, 1000);
  }

  /**
   * 检查是否被中断
   */
  public isAborted(): boolean {
    return this.abortController?.signal.aborted || false;
  }
}