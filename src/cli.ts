#!/usr/bin/env node

// 设置环境变量来抑制 LangChain 警告信息
process.env.LANGCHAIN_VERBOSE = 'false';
process.env.LANGCHAIN_TRACING_V2 = 'false';
process.env.LANGCHAIN_CALLBACKS_BACKGROUND = 'false';

// 重写 console.warn 来过滤 LangChain 警告
const originalWarn = console.warn;
console.warn = (...args: any[]) => {
  const message = args.join(' ');
  // 过滤特定的 LangChain 工具调用警告信息
  if (message.includes('New LangChain packages are available that more efficiently handle tool calling')) {
    return;
  }
  originalWarn.apply(console, args);
};

import { Command } from 'commander';
import inquirer from 'inquirer';
import chalk from 'chalk';
import ora from 'ora';
import * as dotenv from 'dotenv';
import { ConfigManager } from './core/config';
import { ChatManager } from './core/chat';
import { FileScanner } from './utils/fileScanner';
import { MemoryManager } from './core/memory';
import { InteractiveCLI } from './core/interactive';

dotenv.config();

const program = new Command();
const config = new ConfigManager();
const chat = new ChatManager(config);

// 提取配置命令逻辑为独立函数
async function runConfigCommand(): Promise<void> {
  console.log(chalk.blue.bold('⚙️  Chater 配置'));
  
  const currentConfig = config.getConfig();
  const choices = [
    { name: '设置 DeepSeek API 密钥', value: 'deepseek' },
    { name: '设置 OpenAI API 密钥', value: 'openai' },
    { name: '设置 Anthropic API 密钥', value: 'anthropic' },
    { name: '查看当前配置', value: 'view' },
    { name: '退出', value: 'exit' }
  ];

  while (true) {
    const { action } = await inquirer.prompt([
      {
        type: 'list',
        name: 'action',
        message: '选择操作:',
        choices
      }
    ]);

    if (action === 'exit') {
      break;
    }

    if (action === 'view') {
      console.log(chalk.blue('\n📋 当前配置:'));
      console.log(`  默认模型: ${currentConfig.defaultModel}`);
      console.log(`  DeepSeek API: ${currentConfig.apiKeys.deepseek ? '已设置' : '未设置'}`);
      console.log(`  OpenAI API: ${currentConfig.apiKeys.openai ? '已设置' : '未设置'}`);
      console.log(`  Anthropic API: ${currentConfig.apiKeys.anthropic ? '已设置' : '未设置'}`);
      console.log(`  配置文件: ${config.getConfigPath()}\n`);
      continue;
    }

    if (action === 'deepseek' || action === 'openai' || action === 'anthropic') {
      const { apiKey } = await inquirer.prompt([
        {
          type: 'password',
          name: 'apiKey',
          message: `输入 ${action.toUpperCase()} API 密钥:`,
          mask: '*'
        }
      ]);

      if (apiKey.trim()) {
        config.setApiKey(action as any, apiKey.trim());
        console.log(chalk.green(`✅ ${action.toUpperCase()} API 密钥已保存\n`));
      }
    }
  }
}

program
  .name('chater')
  .description('智能AI开发助手CLI工具')
  .version('0.3.3')
  .option('--modern', '启用现代化界面 (默认)')
  .option('--classic', '使用传统界面')
  .action(async (options) => {
    // 默认启动现代化界面，除非明确指定 --classic
    const interactive = new InteractiveCLI();
    const useModernUI = options.classic !== true; // 默认为现代化界面

    if (useModernUI) {
      console.log(chalk.cyan('🚀 启动现代化界面...'));
    } else {
      console.log(chalk.blue.bold('🔧 启动传统界面...'));
    }

    await interactive.start(useModernUI);
  });

program
  .command('chat')
  .description('启动传统聊天模式（仅对话）')
  .option('-m, --model <model>', '指定要使用的模型')
  .action(async (options) => {
    console.log(chalk.blue.bold('🤖 Chater AI 聊天代理'));
    console.log(chalk.gray('🧠 智能模型选择: 简单问题用 deepseek-chat，复杂问题用 deepseek-reasoner'));
    console.log(chalk.gray('输入 /exit 退出，/help 查看帮助\n'));

    const availableModels = chat.getAvailableModels();
    if (availableModels.length === 0) {
      console.log(chalk.red('❌ 没有可用的模型。请先配置 API 密钥。'));
      console.log(chalk.yellow('运行 "chater config" 来设置 API 密钥'));
      
      // 提示用户设置 API 密钥
      const { shouldConfig } = await inquirer.prompt([
        {
          type: 'confirm',
          name: 'shouldConfig',
          message: '是否现在就配置 API 密钥？',
          default: true
        }
      ]);

      if (shouldConfig) {
        // 执行配置命令
        await runConfigCommand();
        // 重新检查可用模型
        const newAvailableModels = chat.getAvailableModels();
        if (newAvailableModels.length === 0) {
          console.log(chalk.red('⚠️  仍然没有可用的模型，退出聊天。'));
          return;
        }
        console.log(chalk.green('✅ API 密钥配置完成，开始聊天！\n'));
      } else {
        return;
      }
    }

    // 如果用户指定了模型，使用指定的模型
    const userSpecifiedModel = options.model && availableModels.includes(options.model) ? options.model : undefined;
    
    if (userSpecifiedModel) {
      console.log(chalk.green(`✅ 使用指定模型: ${userSpecifiedModel}\n`));
    }

    while (true) {
      const { message } = await inquirer.prompt([
        {
          type: 'input',
          name: 'message',
          message: chalk.cyan('你: '),
          validate: (input: string) => {
            if (!input.trim()) {
              return '请输入消息';
            }
            return true;
          }
        }
      ]);

      if (message.toLowerCase() === '/exit') {
        console.log(chalk.yellow('👋 再见！'));
        break;
      }

      if (message.toLowerCase() === '/help') {
        console.log(chalk.blue('\n📖 帮助信息:'));
        console.log('  /exit     - 退出聊天');
        console.log('  /help     - 显示帮助');
        console.log('  /clear    - 清除当前会话');
        console.log('  /models   - 显示可用模型');
        console.log('  /auto     - 显示智能选择状态');
        console.log('  /memory   - 查看项目记忆');
        console.log('  /insight  - 添加重要洞察');
        console.log('  /todo     - 添加待办事项\n');
        continue;
      }

      if (message.toLowerCase() === '/clear') {
        chat.newSession();
        console.log(chalk.green('✅ 会话已清除\n'));
        continue;
      }

      if (message.toLowerCase() === '/models') {
        console.log(chalk.blue('\n🤖 可用模型:'));
        availableModels.forEach(model => {
          const status = availableModels.includes(model) ? chalk.green('✅ 可用') : chalk.red('❌ 不可用');
          console.log(`  ${model} - ${status}`);
        });
        console.log(chalk.gray('\n  💡 使用智能选择: 系统会根据问题复杂度自动选择最适合的模型'));
        console.log();
        continue;
      }

      if (message.toLowerCase() === '/auto') {
        console.log(chalk.blue('\n🧠 智能模型选择状态:'));
        console.log('  💬 简单问题 → deepseek-chat (快速响应)');
        console.log('  🔬 复杂问题 → deepseek-reasoner (深度思考)');
        console.log(chalk.gray('  复杂问题包括: 分析、推理、比较、设计、解决问题等\n'));
        continue;
      }

      if (message.toLowerCase() === '/memory') {
        const memory = chat.getMemoryManager();
        const hasMemory = await memory.hasMemory();
        
        if (!hasMemory) {
          console.log(chalk.yellow('📝 没有找到项目记忆文件'));
          console.log(chalk.gray('💡 运行 "chater init" 来创建项目记忆\n'));
        } else {
          const recentEntries = await memory.getRecentEntries(5);
          console.log(chalk.blue('\n🧠 项目记忆:'));
          console.log(`  📂 记忆文件: ${memory.getMemoryFile()}`);
          console.log(`  📄 记录条目: ${recentEntries.length} 条\n`);
          
          if (recentEntries.length > 0) {
            console.log(chalk.blue('最近的记录:'));
            recentEntries.forEach((entry, index) => {
              const icon = entry.type === 'scan' ? '🔍' : 
                          entry.type === 'insight' ? '💡' : 
                          entry.type === 'todo' ? '📝' : '💬';
              console.log(`  ${icon} ${entry.timestamp.toLocaleDateString()} - ${entry.content.slice(0, 60)}...`);
            });
          }
        }
        console.log();
        continue;
      }

      if (message.toLowerCase().startsWith('/insight ')) {
        const insight = message.slice(9).trim();
        if (insight) {
          await chat.addInsight(insight);
          console.log(chalk.green('✅ 洞察已记录\n'));
        } else {
          console.log(chalk.red('❌ 请输入洞察内容，例如: /insight 这个项目使用了微服务架构\n'));
        }
        continue;
      }

      if (message.toLowerCase().startsWith('/todo ')) {
        const todo = message.slice(6).trim();
        if (todo) {
          await chat.addTodo(todo);
          console.log(chalk.green('✅ 待办事项已记录\n'));
        } else {
          console.log(chalk.red('❌ 请输入待办内容，例如: /todo 优化数据库查询性能\n'));
        }
        continue;
      }

      const spinner = ora('🧠 AI 正在思考...').start();

      try {
        const result = await chat.sendMessage(message, userSpecifiedModel);
        spinner.stop();
        
        // 显示使用的模型（如果不是用户指定的）
        if (!userSpecifiedModel) {
          const modelDisplay = result.selectedModel === 'deepseek-reasoner' 
            ? chalk.blue('🔬 deepseek-reasoner')
            : chalk.green('💬 deepseek-chat');
          console.log(chalk.gray(`[智能选择: ${modelDisplay}]`));
        }
        
        console.log(chalk.magenta('\n🤖 AI: ') + result.response + '\n');
      } catch (error) {
        spinner.stop();
        console.log(chalk.red(`\n❌ 错误: ${error instanceof Error ? error.message : '未知错误'}\n`));
      }
    }
  });

program
  .command('init')
  .description('初始化项目：扫描文件结构并创建记忆文件')
  .action(async () => {
    console.log(chalk.blue.bold('🔍 Chater 项目初始化'));
    
    const memory = new MemoryManager();
    const scanner = new FileScanner();
    
    // 检查是否已有记忆文件
    const hasExistingMemory = await memory.hasMemory();
    if (hasExistingMemory) {
      const { overwrite } = await inquirer.prompt([
        {
          type: 'confirm',
          name: 'overwrite',
          message: '检测到已存在的 CHATER.md 文件，是否重新扫描并更新？',
          default: false
        }
      ]);
      
      if (!overwrite) {
        console.log(chalk.yellow('📄 使用现有的记忆文件。'));
        return;
      }
    }

    console.log(chalk.gray('正在扫描当前目录...'));
    const spinner = ora('🔍 扫描项目文件...').start();
    
    try {
      const scan = await scanner.scanDirectory(process.cwd());
      const summary = scanner.generateSummary(scan);
      
      spinner.text = '💾 保存扫描结果...';
      await memory.addScanResult(scan, summary);
      
      spinner.stop();
      
      console.log(chalk.green('✅ 项目扫描完成！'));
      console.log(chalk.blue('\n📊 扫描结果:'));
      console.log(`  📁 总文件数: ${scan.totalFiles}`);
      console.log(`  📏 总大小: ${(scan.totalSize / (1024 * 1024)).toFixed(2)} MB`);
      console.log(`  📝 记忆文件: ${memory.getMemoryFile()}`);
      
      const topTypes = Object.entries(scan.fileTypes)
        .sort(([,a], [,b]) => b - a)
        .slice(0, 3);
      
      if (topTypes.length > 0) {
        console.log(`  🏷️  主要文件类型: ${topTypes.map(([ext, count]) => `${ext}(${count})`).join(', ')}`);
      }
      
      console.log(chalk.gray(`\n💡 提示: 查看 ${memory.getMemoryFile()} 了解完整扫描报告`));
      console.log(chalk.gray('现在可以使用 "chater chat" 开始智能对话，AI 将了解你的项目结构'));
      
    } catch (error) {
      spinner.stop();
      console.log(chalk.red(`❌ 扫描失败: ${error instanceof Error ? error.message : '未知错误'}`));
    }
  });

program
  .command('config')
  .description('配置 API 密钥和设置')
  .action(runConfigCommand);

program
  .command('models')
  .description('列出可用的模型')
  .action(() => {
    const availableModels = chat.getAvailableModels();
    const allModels = Object.keys(config.getConfig().models);
    
    console.log(chalk.blue.bold('🤖 模型列表\n'));
    
    allModels.forEach(model => {
      const modelConfig = config.getConfig().models[model];
      const isAvailable = availableModels.includes(model);
      const status = isAvailable ? chalk.green('✅ 可用') : chalk.red('❌ 不可用');
      
      console.log(`${model}`);
      console.log(`  提供商: ${modelConfig.provider}`);
      console.log(`  状态: ${status}`);
      console.log();
    });
  });

program.parse();