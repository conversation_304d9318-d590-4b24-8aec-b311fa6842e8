import { Chat<PERSON>anager } from './chat';
import { ConfigManager } from './config';
import { ContextManager } from '../utils/contextManager';
import { 
  Task, 
  TaskExecutionPlan, 
  TaskStep, 
  TaskStepType, 
  TaskCategory,
  TaskComplexityAnalysis,
  CreateTaskRequest,
  TaskContext
} from '../types/task';
import { v4 as uuidv4 } from 'uuid';
import chalk from 'chalk';
import ora from 'ora';

export class TaskDecomposer {
  private chatManager: ChatManager;

  constructor(config: ConfigManager) {
    this.chatManager = new ChatManager(config);
  }

  /**
   * 使用 deepseek-reasoner 分解复杂任务
   */
  public async decomposeTask(
    request: CreateTaskRequest,
    complexityAnalysis: TaskComplexityAnalysis,
    category: TaskCategory
  ): Promise<TaskExecutionPlan> {
    
    const spinner = ora('🧠 deepseek-reasoner 正在深度分析和规划任务...').start();
    
    try {
      // 构建和优化提示词
      const basePrompt = this.buildDecompositionPrompt(request, complexityAnalysis, category);
      const { optimizedPrompt, warnings } = ContextManager.optimizePrompt(basePrompt, 'deepseek-reasoner');
      
      // 显示警告信息
      if (warnings.length > 0) {
        spinner.stop();
        console.log(chalk.yellow('⚠️  上下文优化警告:'));
        warnings.forEach(warning => console.log(chalk.gray(`   ${warning}`)));
        spinner.start('🧠 继续任务规划...');
      }
      
      // 检查是否需要分段处理
      const tokenCheck = ContextManager.checkTokenLimit(optimizedPrompt, 'deepseek-reasoner');
      
      let result;
      if (!tokenCheck.isValid && tokenCheck.exceedsBy > 5000) {
        // 如果还是超出太多，使用分段处理
        result = await this.handleLongTaskDecomposition(request, complexityAnalysis, category, spinner);
      } else {
        // 正常处理
        result = await this.chatManager.sendMessage(optimizedPrompt, 'deepseek-reasoner');
      }
      
      spinner.stop();
      console.log(chalk.blue('✅ 任务规划完成'));
      
      // 解析 AI 返回的规划结果
      const plan = this.parseDecompositionResult(result.response, request, complexityAnalysis);
      
      return plan;
      
    } catch (error) {
      spinner.stop();
      console.error(chalk.red('❌ 任务分解失败:'), error);
      
      // 如果是token限制错误，提供友好提示
      if (error instanceof Error && error.message.includes('maximum context length')) {
        console.log(chalk.yellow('💡 提示: 任务描述过长，建议简化描述或分解为更小的子任务'));
        console.log(chalk.gray('    你可以尝试：'));
        console.log(chalk.gray('    1. 缩短任务描述'));
        console.log(chalk.gray('    2. 移除不必要的上下文信息'));
        console.log(chalk.gray('    3. 将大任务拆分为多个小任务'));
      }
      
      throw error;
    }
  }

  /**
   * 处理超长任务的分段分解
   */
  private async handleLongTaskDecomposition(
    request: CreateTaskRequest,
    complexityAnalysis: TaskComplexityAnalysis,
    category: TaskCategory,
    spinner: any
  ): Promise<{ response: string }> {
    
    spinner.text = '📋 任务过长，进行分段规划...';
    
    // 第一阶段：高层规划
    const highLevelPrompt = this.buildHighLevelPrompt(request, complexityAnalysis, category);
    const highLevelResult = await this.chatManager.sendMessage(highLevelPrompt, 'deepseek-reasoner');
    
    spinner.text = '🔍 细化执行步骤...';
    
    // 第二阶段：详细步骤规划
    const detailPrompt = this.buildDetailPrompt(request, highLevelResult.response);
    const detailResult = await this.chatManager.sendMessage(detailPrompt, 'deepseek-reasoner');
    
    // 合并结果
    const combinedResponse = this.combineDecompositionResults(highLevelResult.response, detailResult.response);
    
    return { response: combinedResponse };
  }

  /**
   * 构建任务分解的提示词（优化版）
   */
  private buildDecompositionPrompt(
    request: CreateTaskRequest,
    analysis: TaskComplexityAnalysis,
    category: TaskCategory
  ): string {
    // 基础信息
    const baseInfo = `任务规划：${request.description}

复杂度：${analysis.complexity} (${analysis.score}/100) | 类型：${category} | 预估：${analysis.estimatedTime}分钟

关键因素：${analysis.factors.slice(0, 3).map(f => `${f.name}(${f.score})`).join(', ')}`;

    // 项目上下文（简化）
    const contextInfo = request.context?.relevantFiles?.length ? 
      `\n相关文件：${request.context.relevantFiles.slice(0, 5).join(', ')}${request.context.relevantFiles.length > 5 ? '...' : ''}` : '';

    return `${baseInfo}${contextInfo}

请分解为可执行步骤，格式：

**步骤 N: [标题]**
- 类型: [分析/文件操作/代码生成/测试/验证/部署]
- 描述: [具体要做什么]
- 时间: [分钟]
- 工具: [需要的工具]
- 输出: [预期结果]
- 成功标准: [验证方法]
- 依赖: [前置步骤]

要求：
1. 步骤具体可执行
2. 明确依赖关系
3. 包含验证标准
4. 考虑错误处理

开始规划：`;
  }

  /**
   * 构建高层规划提示词
   */
  private buildHighLevelPrompt(
    request: CreateTaskRequest,
    analysis: TaskComplexityAnalysis,
    category: TaskCategory
  ): string {
    return `任务概要规划：${request.description}

复杂度：${analysis.complexity} (${analysis.score}/100)

请提供高层执行框架：

1. **任务目标**：[核心目标是什么]
2. **主要阶段**：[分为3-5个主要阶段]
3. **关键风险**：[主要风险点]
4. **成功标准**：[如何衡量成功]

要求简洁明确，重点关注整体思路。`;
  }

  /**
   * 构建详细步骤提示词
   */
  private buildDetailPrompt(request: CreateTaskRequest, highLevelPlan: string): string {
    const planSummary = highLevelPlan.substring(0, 500) + (highLevelPlan.length > 500 ? '...' : '');
    
    return `基于高层规划细化步骤：

高层计划摘要：
${planSummary}

任务：${request.description}

请详细分解执行步骤：

**步骤 N: [标题]**
- 类型: [操作类型]
- 描述: [具体操作]
- 时间: [分钟]
- 工具: [工具名]
- 输出: [结果]
- 成功标准: [验证]

要求：每步具体可执行，限制在10步以内。`;
  }

  /**
   * 合并分解结果
   */
  private combineDecompositionResults(highLevel: string, details: string): string {
    return `## 任务规划结果

### 高层规划
${highLevel}

### 详细步骤
${details}

### 执行说明
以上步骤已经过优化以适应上下文长度限制。`;
  }

  /**
   * 解析 AI 返回的分解结果
   */
  private parseDecompositionResult(
    aiResponse: string, 
    request: CreateTaskRequest,
    analysis: TaskComplexityAnalysis
  ): TaskExecutionPlan {
    
    // 解析 AI 返回的结构化内容
    const plan: TaskExecutionPlan = {
      id: uuidv4(),
      title: this.extractTitle(aiResponse) || '任务执行计划',
      description: this.extractDescription(aiResponse) || request.description,
      reasoning: this.extractReasoning(aiResponse),
      steps: this.extractSteps(aiResponse),
      dependencies: this.extractDependencies(aiResponse),
      estimatedTime: this.extractEstimatedTime(aiResponse) || analysis.estimatedTime,
      riskAssessment: this.extractRiskAssessment(aiResponse)
    };

    return plan;
  }

  /**
   * 提取计划标题
   */
  private extractTitle(response: string): string | null {
    const titleMatch = response.match(/\*\*标题\*\*:\s*(.+)/);
    return titleMatch ? titleMatch[1].trim() : null;
  }

  /**
   * 提取计划描述
   */
  private extractDescription(response: string): string | null {
    const descMatch = response.match(/\*\*描述\*\*:\s*(.+)/);
    return descMatch ? descMatch[1].trim() : null;
  }

  /**
   * 提取推理过程
   */
  private extractReasoning(response: string): string {
    const sections = [
      this.extractSection(response, '## 深度分析'),
      this.extractSection(response, '### 任务理解'),
      this.extractSection(response, '### 风险评估'),
      this.extractSection(response, '### 策略选择')
    ].filter(Boolean);
    
    return sections.join('\n\n');
  }

  /**
   * 提取执行步骤
   */
  private extractSteps(response: string): TaskStep[] {
    const steps: TaskStep[] = [];
    
    // 匹配步骤模式
    const stepPattern = /\*\*步骤\s+(\d+):\s*(.+?)\*\*\n([\s\S]*?)(?=\*\*步骤\s+\d+:|## |$)/g;
    let match;
    
    while ((match = stepPattern.exec(response)) !== null) {
      const stepNumber = parseInt(match[1]);
      const stepTitle = match[2].trim();
      const stepContent = match[3].trim();
      
      const step: TaskStep = {
        id: uuidv4(),
        title: stepTitle,
        description: this.extractStepField(stepContent, '描述') || stepTitle,
        type: this.parseStepType(this.extractStepField(stepContent, '类型')),
        status: 'pending',
        toolCalls: this.parseToolCalls(this.extractStepField(stepContent, '需要工具')),
        expectedOutput: this.extractStepField(stepContent, '期望输出') || '',
        estimatedDuration: this.parseTime(this.extractStepField(stepContent, '预估时间')) || 10,
        dependencies: this.parseStepDependencies(this.extractStepField(stepContent, '依赖步骤')),
        blockers: [],
        successCriteria: this.parseSuccessCriteria(this.extractStepField(stepContent, '成功标准'))
      };
      
      steps.push(step);
    }
    
    return steps;
  }

  /**
   * 提取依赖项
   */
  private extractDependencies(response: string): string[] {
    const depsText = this.extractStepField(response, '关键依赖');
    if (!depsText) return [];
    
    return depsText.split(/[,，]/).map(dep => dep.trim()).filter(Boolean);
  }

  /**
   * 提取预估时间
   */
  private extractEstimatedTime(response: string): number | null {
    const timeText = this.extractStepField(response, '预估总时间');
    return timeText ? this.parseTime(timeText) : null;
  }

  /**
   * 提取风险评估
   */
  private extractRiskAssessment(response: string): string {
    return this.extractSection(response, '### 风险评估') || '';
  }

  /**
   * 辅助方法：提取章节内容
   */
  private extractSection(text: string, sectionTitle: string): string | null {
    const regex = new RegExp(`${sectionTitle}\\n([\\s\\S]*?)(?=\\n## |\\n### |$)`);
    const match = text.match(regex);
    return match ? match[1].trim() : null;
  }

  /**
   * 辅助方法：提取步骤字段
   */
  private extractStepField(stepContent: string, fieldName: string): string | null {
    const regex = new RegExp(`-\\s*${fieldName}:\\s*(.+?)(?=\\n-|$)`, 'i');
    const match = stepContent.match(regex);
    return match ? match[1].trim() : null;
  }

  /**
   * 解析步骤类型
   */
  private parseStepType(typeText: string | null): TaskStepType {
    if (!typeText) return 'analysis';
    
    const typeMap: Record<string, TaskStepType> = {
      '分析': 'analysis',
      '文件操作': 'file_operation',
      '代码生成': 'code_generation',
      '测试': 'testing',
      '验证': 'validation',
      '部署': 'deployment',
      '文档': 'documentation',
      '研究': 'research',
      '用户交互': 'user_interaction',
      '工具执行': 'tool_execution'
    };
    
    return typeMap[typeText] || 'analysis';
  }

  /**
   * 解析工具调用
   */
  private parseToolCalls(toolText: string | null): string[] {
    if (!toolText) return [];
    
    return toolText.split(/[,，]/).map(tool => tool.trim()).filter(Boolean);
  }

  /**
   * 解析步骤依赖
   */
  private parseStepDependencies(depsText: string | null): string[] {
    if (!depsText || depsText.toLowerCase().includes('无')) return [];
    
    return depsText.split(/[,，]/).map(dep => dep.trim()).filter(Boolean);
  }

  /**
   * 解析成功标准
   */
  private parseSuccessCriteria(criteriaText: string | null): string[] {
    if (!criteriaText) return [];
    
    return criteriaText.split(/[;；]/).map(criteria => criteria.trim()).filter(Boolean);
  }

  /**
   * 解析时间（分钟）
   */
  private parseTime(timeText: string | null): number | null {
    if (!timeText) return null;
    
    const match = timeText.match(/(\d+)/);
    return match ? parseInt(match[1]) : null;
  }

  /**
   * 验证执行计划的完整性
   */
  public validateExecutionPlan(plan: TaskExecutionPlan): { isValid: boolean; issues: string[] } {
    const issues: string[] = [];
    
    if (!plan.title) {
      issues.push('缺少计划标题');
    }
    
    if (!plan.description) {
      issues.push('缺少计划描述');
    }
    
    if (plan.steps.length === 0) {
      issues.push('没有执行步骤');
    }
    
    // 验证步骤
    plan.steps.forEach((step, index) => {
      if (!step.title) {
        issues.push(`步骤 ${index + 1} 缺少标题`);
      }
      
      if (!step.description) {
        issues.push(`步骤 ${index + 1} 缺少描述`);
      }
      
      if (step.estimatedDuration <= 0) {
        issues.push(`步骤 ${index + 1} 预估时间无效`);
      }
    });
    
    // 验证依赖关系
    const stepTitles = plan.steps.map(s => s.title);
    plan.steps.forEach((step, index) => {
      step.dependencies.forEach(dep => {
        if (!stepTitles.includes(dep)) {
          issues.push(`步骤 ${index + 1} 的依赖 "${dep}" 不存在`);
        }
      });
    });
    
    return {
      isValid: issues.length === 0,
      issues
    };
  }
}