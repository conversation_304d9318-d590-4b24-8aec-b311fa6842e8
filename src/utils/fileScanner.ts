import * as fs from 'fs';
import * as path from 'path';

export interface FileInfo {
  path: string;
  name: string;
  type: 'file' | 'directory';
  size: number;
  extension?: string;
  isReadable: boolean;
  isWritable: boolean;
  lastModified: Date;
}

export interface ProjectScan {
  rootPath: string;
  files: FileInfo[];
  totalFiles: number;
  totalSize: number;
  fileTypes: Record<string, number>;
  largestFiles: FileInfo[];
  recentFiles: FileInfo[];
  scannedAt: Date;
}

export class FileScanner {
  private readonly IGNORED_PATTERNS = [
    /node_modules/,
    /\.git/,
    /dist/,
    /build/,
    /coverage/,
    /\.DS_Store/,
    /\.env/,
    /\.vscode/,
    /\.idea/,
    /target/,
    /\.next/,
    /\.nuxt/,
    /out/,
    /logs?/,
    /tmp/,
    /temp/
  ];

  private readonly MAX_FILE_SIZE = 50 * 1024 * 1024; // 50MB
  private readonly MAX_FILES = 1000;

  public async scanDirectory(rootPath: string): Promise<ProjectScan> {
    const files: FileInfo[] = [];
    const fileTypes: Record<string, number> = {};
    let totalSize = 0;

    await this.scanRecursively(rootPath, rootPath, files, fileTypes);

    totalSize = files.reduce((sum, file) => sum + file.size, 0);

    // 获取最大的5个文件
    const largestFiles = files
      .filter(f => f.type === 'file')
      .sort((a, b) => b.size - a.size)
      .slice(0, 5);

    // 获取最近修改的5个文件
    const recentFiles = files
      .filter(f => f.type === 'file')
      .sort((a, b) => b.lastModified.getTime() - a.lastModified.getTime())
      .slice(0, 5);

    return {
      rootPath,
      files,
      totalFiles: files.filter(f => f.type === 'file').length,
      totalSize,
      fileTypes,
      largestFiles,
      recentFiles,
      scannedAt: new Date()
    };
  }

  private async scanRecursively(
    currentPath: string,
    rootPath: string,
    files: FileInfo[],
    fileTypes: Record<string, number>
  ): Promise<void> {
    if (files.length >= this.MAX_FILES) {
      return;
    }

    try {
      const entries = await fs.promises.readdir(currentPath, { withFileTypes: true });

      for (const entry of entries) {
        const fullPath = path.join(currentPath, entry.name);
        const relativePath = path.relative(rootPath, fullPath);

        // 检查是否应该忽略
        if (this.shouldIgnore(relativePath)) {
          continue;
        }

        try {
          const stats = await fs.promises.stat(fullPath);
          
          // 跳过过大的文件
          if (entry.isFile() && stats.size > this.MAX_FILE_SIZE) {
            continue;
          }

          const fileInfo: FileInfo = {
            path: relativePath,
            name: entry.name,
            type: entry.isDirectory() ? 'directory' : 'file',
            size: stats.size,
            extension: entry.isFile() ? path.extname(entry.name).toLowerCase() : undefined,
            isReadable: await this.checkAccess(fullPath, fs.constants.R_OK),
            isWritable: await this.checkAccess(fullPath, fs.constants.W_OK),
            lastModified: stats.mtime
          };

          files.push(fileInfo);

          // 统计文件类型
          if (entry.isFile() && fileInfo.extension) {
            fileTypes[fileInfo.extension] = (fileTypes[fileInfo.extension] || 0) + 1;
          }

          // 递归扫描目录
          if (entry.isDirectory()) {
            await this.scanRecursively(fullPath, rootPath, files, fileTypes);
          }
        } catch (error) {
          // 忽略无法访问的文件/目录
          console.warn(`无法访问 ${fullPath}:`, (error as Error).message);
        }
      }
    } catch (error) {
      console.warn(`无法读取目录 ${currentPath}:`, (error as Error).message);
    }
  }

  private shouldIgnore(relativePath: string): boolean {
    return this.IGNORED_PATTERNS.some(pattern => pattern.test(relativePath));
  }

  private async checkAccess(filePath: string, mode: number): Promise<boolean> {
    try {
      await fs.promises.access(filePath, mode);
      return true;
    } catch {
      return false;
    }
  }

  public generateSummary(scan: ProjectScan): string {
    const { totalFiles, totalSize, fileTypes, largestFiles, recentFiles } = scan;
    
    const sizeInMB = (totalSize / (1024 * 1024)).toFixed(2);
    const topTypes = Object.entries(fileTypes)
      .sort(([,a], [,b]) => b - a)
      .slice(0, 5);

    const summary = `# 项目扫描报告

## 基本信息
- **扫描时间**: ${scan.scannedAt.toLocaleString('zh-CN')}
- **根目录**: ${scan.rootPath}
- **文件总数**: ${totalFiles}
- **总大小**: ${sizeInMB} MB

## 文件类型分布
${topTypes.map(([ext, count]) => `- **${ext || '无扩展名'}**: ${count} 个文件`).join('\n')}

## 最大文件
${largestFiles.map(f => `- **${f.name}** (${f.path}) - ${(f.size / 1024).toFixed(1)} KB`).join('\n')}

## 最近修改的文件
${recentFiles.map(f => `- **${f.name}** (${f.path}) - ${f.lastModified.toLocaleDateString('zh-CN')}`).join('\n')}

---
*此报告由 Chater AI 生成*
`;

    return summary;
  }
}