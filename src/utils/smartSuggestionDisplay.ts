import chalk from 'chalk';
import { SmartSuggestion } from './smartAutoComplete';

export class SmartSuggestionDisplay {
  
  /**
   * 显示智能建议列表
   */
  public static showSmartSuggestions(input: string, suggestions: SmartSuggestion[]): void {
    if (suggestions.length === 0) return;

    console.log(chalk.gray('\n💡 智能建议:'));
    
    // 按类型分组显示
    const grouped = this.groupSuggestionsByType(suggestions);
    
    Object.entries(grouped).forEach(([type, items], groupIndex) => {
      if (items.length === 0) return;
      
      // 显示分组标题
      const typeTitle = this.getTypeTitle(type);
      console.log(chalk.cyan(`\n${typeTitle}:`));
      
      // 显示建议项
      items.slice(0, 4).forEach((suggestion, index) => {
        const icon = suggestion.icon || this.getDefaultIcon(type);
        const priority = '★'.repeat(Math.min(3, Math.max(1, Math.floor(suggestion.priority / 3))));
        
        console.log(
          `  ${chalk.yellow(icon)} ${chalk.white(suggestion.text)}` +
          (suggestion.description ? chalk.gray(` - ${suggestion.description}`) : '') +
          chalk.dim(` ${priority}`)
        );
      });
      
      if (items.length > 4) {
        console.log(chalk.gray(`  ... 还有 ${items.length - 4} 个建议`));
      }
    });
    
    console.log(chalk.gray('\n💡 按 Tab 键使用建议，继续输入以筛选\n'));
  }

  /**
   * 显示实时输入提示
   */
  public static showLiveHint(input: string, suggestions: SmartSuggestion[]): string {
    if (!input || suggestions.length === 0) return '';
    
    const topSuggestion = suggestions[0];
    if (!topSuggestion) return '';
    
    // 智能补全预览
    const commonPrefix = this.findCommonPrefix(input, topSuggestion.text);
    if (commonPrefix.length > input.length) {
      const completion = commonPrefix.slice(input.length);
      return chalk.gray(completion);
    }
    
    return '';
  }

  /**
   * 显示紧凑的建议摘要
   */
  public static showCompactSuggestions(input: string, suggestions: SmartSuggestion[]): void {
    if (suggestions.length === 0) return;

    const topSuggestions = suggestions.slice(0, 3);
    const suggestionTexts = topSuggestions.map(s => {
      const icon = s.icon || this.getDefaultIcon(s.type);
      return `${icon} ${s.text}`;
    });
    
    console.log(
      chalk.gray('💡 建议: ') +
      suggestionTexts.join(chalk.gray(' • ')) +
      (suggestions.length > 3 ? chalk.gray(` +${suggestions.length - 3}`) : '')
    );
  }

  /**
   * 显示交互式建议选择
   */
  public static async showInteractiveSuggestions(
    input: string, 
    suggestions: SmartSuggestion[]
  ): Promise<string | null> {
    if (suggestions.length === 0) return null;

    const inquirer = require('inquirer');
    
    const choices = suggestions.slice(0, 8).map((suggestion, index) => {
      const icon = suggestion.icon || this.getDefaultIcon(suggestion.type);
      const priority = '★'.repeat(Math.min(3, Math.max(1, Math.floor(suggestion.priority / 3))));
      
      return {
        name: `${icon} ${suggestion.text}${suggestion.description ? chalk.gray(` - ${suggestion.description}`) : ''} ${chalk.dim(priority)}`,
        value: suggestion.text,
        short: suggestion.text
      };
    });

    // 添加"继续当前输入"选项
    choices.unshift({
      name: `💬 继续输入: ${chalk.cyan(input)}`,
      value: '__CONTINUE__',
      short: input
    });

    try {
      const { selection } = await inquirer.prompt([
        {
          type: 'list',
          name: 'selection',
          message: '选择建议或继续输入:',
          choices,
          pageSize: 10
        }
      ]);

      return selection === '__CONTINUE__' ? null : selection;
    } catch (error) {
      return null; // 用户取消
    }
  }

  /**
   * 显示上下文相关的提示
   */
  public static showContextualHint(input: string): void {
    if (!input.trim()) {
      console.log(chalk.gray('💡 提示: 输入 /help 查看命令，或直接与 AI 对话'));
      return;
    }

    const trimmed = input.trim();
    
    if (trimmed.startsWith('/')) {
      console.log(chalk.gray('💡 命令模式: 输入命令名称，如 help, exit, config'));
    } else if (trimmed.startsWith('!')) {
      console.log(chalk.gray('💡 系统命令: 输入要执行的终端命令'));
    } else if (trimmed.startsWith('@')) {
      console.log(chalk.gray('💡 文件操作: 输入文件路径，可加上操作说明让 AI 处理'));
    } else if (trimmed.startsWith('#')) {
      console.log(chalk.gray('💡 记忆操作: 记录洞察、待办或搜索项目记忆'));
    } else {
      // 分析输入内容给出相关提示
      const lowercaseInput = trimmed.toLowerCase();
      
      if (lowercaseInput.includes('帮') || lowercaseInput.includes('help')) {
        console.log(chalk.gray('💡 AI 助手: 我可以帮您分析代码、解决问题、重构项目'));
      } else if (lowercaseInput.includes('文件') || lowercaseInput.includes('file')) {
        console.log(chalk.gray('💡 文件操作: 使用 @ 前缀快速操作文件，如 @package.json 分析依赖'));
      } else if (lowercaseInput.includes('代码') || lowercaseInput.includes('code')) {
        console.log(chalk.gray('💡 代码分析: 我可以解释代码、找bug、提供优化建议'));
      } else {
        console.log(chalk.gray('💡 AI 对话: 您可以直接问我任何开发相关的问题'));
      }
    }
  }

  /**
   * 显示快速操作面板
   */
  public static showQuickActions(): void {
    console.log(chalk.blue('\n🚀 快速操作:'));
    console.log(chalk.yellow('  /help') + chalk.gray('     - 显示完整帮助'));
    console.log(chalk.yellow('  /exit') + chalk.gray('     - 退出程序'));
    console.log(chalk.yellow('  !git status') + chalk.gray(' - 查看Git状态'));
    console.log(chalk.yellow('  @package.json') + chalk.gray(' - 查看项目配置'));
    console.log(chalk.yellow('  #记录发现') + chalk.gray('  - 添加项目洞察'));
    console.log(chalk.gray('\n💡 输入任意内容开始与 AI 对话\n'));
  }

  private static groupSuggestionsByType(suggestions: SmartSuggestion[]): Record<string, SmartSuggestion[]> {
    const groups: Record<string, SmartSuggestion[]> = {
      command: [],
      ai_template: [],
      file: [],
      bash: [],
      memory: [],
      history: []
    };

    suggestions.forEach(suggestion => {
      if (groups[suggestion.type]) {
        groups[suggestion.type].push(suggestion);
      }
    });

    return groups;
  }

  private static getTypeTitle(type: string): string {
    const titles: Record<string, string> = {
      command: '🔧 命令',
      ai_template: '🤖 AI 助手',
      file: '📁 文件操作',
      bash: '⚡ 系统命令',
      memory: '🧠 项目记忆',
      history: '📚 历史记录'
    };
    
    return titles[type] || '💡 建议';
  }

  private static getDefaultIcon(type: string): string {
    const icons: Record<string, string> = {
      command: '🔧',
      ai_template: '🤖',
      file: '📄',
      bash: '⚡',
      memory: '🧠',
      history: '📚'
    };
    
    return icons[type] || '💡';
  }

  private static findCommonPrefix(input: string, suggestion: string): string {
    const minLength = Math.min(input.length, suggestion.length);
    let commonLength = 0;
    
    for (let i = 0; i < minLength; i++) {
      if (input[i].toLowerCase() === suggestion[i].toLowerCase()) {
        commonLength = i + 1;
      } else {
        break;
      }
    }
    
    // 只有当共同前缀比输入长时才返回补全
    if (commonLength >= input.length && suggestion.length > input.length) {
      return suggestion.slice(0, commonLength + Math.min(10, suggestion.length - commonLength));
    }
    
    return input;
  }

  /**
   * 显示键盘快捷键提示
   */
  public static showKeyboardHints(): void {
    console.log(chalk.gray('\n⌨️  键盘提示:'));
    console.log(chalk.gray('  Tab    - 接受建议'));
    console.log(chalk.gray('  ↑/↓   - 浏览历史'));
    console.log(chalk.gray('  Ctrl+C - 强制退出'));
    console.log(chalk.gray('  ?      - 显示帮助\n'));
  }

  /**
   * 清理之前的建议显示
   */
  public static clearSuggestions(): void {
    // 清除之前的建议显示（如果需要的话）
    process.stdout.write('\x1b[2K\r'); // 清除当前行
  }
}