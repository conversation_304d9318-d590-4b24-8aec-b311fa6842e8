import inquirer from 'inquirer';
import chalk from 'chalk';
import { existsSync, readdirSync, statSync } from 'fs';
import { join } from 'path';
import { SuggestionDisplay } from './suggestionDisplay';

export interface AutoCompleteSource {
  type: 'command' | 'file' | 'memory' | 'bash';
  suggestions: string[];
  prefix?: string;
}

export class AdvancedInputHandler {
  private commandHistory: string[] = [];
  private maxHistorySize = 100;
  private projectPath: string;

  constructor(projectPath: string = process.cwd()) {
    this.projectPath = projectPath;
    this.loadHistory();
  }

  /**
   * 获取自动补全建议
   */
  public getAutoCompleteSuggestions(input: string): string[] {
    const trimmed = input.trim();
    
    if (trimmed.startsWith('/')) {
      return this.getCommandSuggestions(trimmed.slice(1));
    }
    
    if (trimmed.startsWith('!')) {
      return this.getBashSuggestions(trimmed.slice(1));
    }
    
    if (trimmed.startsWith('@')) {
      return this.getFileSuggestions(trimmed.slice(1));
    }
    
    if (trimmed.startsWith('#')) {
      return this.getMemorySuggestions(trimmed.slice(1));
    }

    // 普通输入的智能建议
    return this.getGeneralSuggestions(trimmed);
  }

  /**
   * 获取命令建议
   */
  private getCommandSuggestions(partial: string): string[] {
    const commands = [
      'help', 'config', 'models', 'memory', 'init', 'clear', 'exit',
      'shortcuts', 'pwd', 'ls', 'insight', 'todo'
    ];
    
    return commands
      .filter(cmd => cmd.startsWith(partial.toLowerCase()))
      .map(cmd => `/${cmd}`);
  }

  /**
   * 获取 bash 命令建议
   */
  private getBashSuggestions(partial: string): string[] {
    const commonCommands = [
      'git status', 'git add .', 'git commit', 'git push', 'git pull',
      'npm install', 'npm run build', 'npm run dev', 'npm test',
      'ls -la', 'pwd', 'cd', 'mkdir', 'rm', 'cp', 'mv',
      'docker ps', 'docker build', 'docker run',
      'yarn install', 'yarn build', 'yarn dev',
      'code .', 'vim', 'nano'
    ];
    
    return commonCommands
      .filter(cmd => cmd.startsWith(partial.toLowerCase()))
      .map(cmd => `!${cmd}`);
  }

  /**
   * 获取文件路径建议
   */
  private getFileSuggestions(partial: string): string[] {
    try {
      const suggestions: string[] = [];
      
      // 如果是空的，显示常见文件
      if (!partial) {
        const commonFiles = [
          'package.json', 'README.md', 'tsconfig.json', '.gitignore',
          'src/', 'dist/', 'node_modules/'
        ];
        return commonFiles
          .filter(file => existsSync(join(this.projectPath, file)))
          .map(file => `@${file}`);
      }

      // 解析路径
      const lastSlash = partial.lastIndexOf('/');
      const dirPath = lastSlash >= 0 ? partial.substring(0, lastSlash + 1) : '';
      const fileName = lastSlash >= 0 ? partial.substring(lastSlash + 1) : partial;
      
      const searchDir = join(this.projectPath, dirPath || '.');
      
      if (existsSync(searchDir) && statSync(searchDir).isDirectory()) {
        const entries = readdirSync(searchDir);
        
        for (const entry of entries) {
          if (entry.startsWith('.') && !fileName.startsWith('.')) continue;
          if (entry.toLowerCase().includes(fileName.toLowerCase())) {
            const fullPath = dirPath + entry;
            const entryPath = join(searchDir, entry);
            const isDir = statSync(entryPath).isDirectory();
            suggestions.push(`@${fullPath}${isDir ? '/' : ''}`);
          }
        }
      }
      
      return suggestions.slice(0, 10); // 限制建议数量
    } catch (error) {
      return [];
    }
  }

  /**
   * 获取记忆相关建议
   */
  private getMemorySuggestions(partial: string): string[] {
    const memoryCommands = [
      'view', 'search', 'insight', 'todo', 'clear'
    ];
    
    if (!partial) {
      return memoryCommands.map(cmd => `#${cmd}`);
    }
    
    return memoryCommands
      .filter(cmd => cmd.startsWith(partial.toLowerCase()))
      .map(cmd => `#${cmd}`);
  }

  /**
   * 获取一般建议（基于历史）
   */
  private getGeneralSuggestions(partial: string): string[] {
    if (!partial || partial.length < 2) return [];
    
    return this.commandHistory
      .filter(cmd => cmd.toLowerCase().includes(partial.toLowerCase()))
      .slice(0, 5);
  }

  /**
   * 添加到历史记录
   */
  public addToHistory(command: string): void {
    if (!command.trim()) return;
    
    // 移除重复项
    const index = this.commandHistory.indexOf(command);
    if (index > -1) {
      this.commandHistory.splice(index, 1);
    }
    
    // 添加到开头
    this.commandHistory.unshift(command);
    
    // 保持历史记录大小
    if (this.commandHistory.length > this.maxHistorySize) {
      this.commandHistory = this.commandHistory.slice(0, this.maxHistorySize);
    }
    
    this.saveHistory();
  }

  /**
   * 获取历史记录
   */
  public getHistory(): string[] {
    return [...this.commandHistory];
  }

  /**
   * 创建增强的输入提示
   */
  public async createEnhancedInput(message: string): Promise<string> {
    // 处理特殊输入
    if (message.includes('?')) {
      SuggestionDisplay.showQuickHelp();
    }
    
    // 显示输入提示
    SuggestionDisplay.showInputHint();
    
    const { input } = await inquirer.prompt([
      {
        type: 'input',
        name: 'input',
        message,
        validate: (input: string) => {
          // 特殊命令处理
          if (input.trim() === '?') {
            return true; // 允许 ? 输入
          }
          return input.trim().length > 0 || '请输入内容';
        },
        transformer: (input: string) => {
          // 实时显示建议计数
          if (input.length > 0) {
            const suggestions = this.getAutoCompleteSuggestions(input);
            return input + SuggestionDisplay.showLiveCounter(input, suggestions.length);
          }
          return input;
        }
      }
    ]);
    
    // 处理特殊输入
    if (input.trim() === '?') {
      SuggestionDisplay.showQuickHelp();
      return this.createEnhancedInput(message); // 重新输入
    }
    
    // 显示建议
    if (input.trim() && input.length > 1) {
      const suggestions = this.getAutoCompleteSuggestions(input);
      SuggestionDisplay.showSuggestions(input, suggestions);
    }
    
    return input;
  }


  /**
   * 保存历史记录到文件
   */
  private saveHistory(): void {
    try {
      const historyFile = join(this.projectPath, '.chater_history');
      const fs = require('fs');
      fs.writeFileSync(historyFile, JSON.stringify(this.commandHistory, null, 2));
    } catch (error) {
      // 静默失败，不影响用户体验
    }
  }

  /**
   * 从文件加载历史记录
   */
  private loadHistory(): void {
    try {
      const historyFile = join(this.projectPath, '.chater_history');
      if (existsSync(historyFile)) {
        const fs = require('fs');
        const content = fs.readFileSync(historyFile, 'utf8');
        this.commandHistory = JSON.parse(content) || [];
      }
    } catch (error) {
      this.commandHistory = [];
    }
  }

  /**
   * 显示提示信息
   */
  public showHints(): void {
    console.log(chalk.gray('\n💡 快捷键提示:'));
    console.log(chalk.gray('  / - 命令补全    ! - bash 命令    @ - 文件路径    # - 记忆操作'));
    console.log(chalk.gray('  ↑↓ - 历史记录   Tab - 自动补全   Ctrl+C - 退出\n'));
  }
}