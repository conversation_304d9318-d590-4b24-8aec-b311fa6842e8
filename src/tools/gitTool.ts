import { execSync } from 'child_process';
import { Tool } from '../types';

export const gitTool: Tool = {
  name: 'git',
  description: '执行 Git 命令来管理代码版本控制。可以执行 status, add, commit, push, pull, log 等常用操作。',
  parameters: {
    type: 'object',
    properties: {
      command: {
        type: 'string',
        description: 'Git 子命令',
        enum: ['status', 'add', 'commit', 'push', 'pull', 'log', 'diff', 'branch', 'checkout']
      },
      options: {
        type: 'array',
        description: '命令选项和参数，例如文件名、分支名、提交信息等'
      },
      message: {
        type: 'string',
        description: '提交信息（仅用于 commit 命令）'
      }
    },
    required: ['command']
  },
  execute: async (args: Record<string, any>) => {
    const { command, options = [], message } = args;
    
    try {
      let gitCommand = `git ${command}`;
      
      // 特殊处理 commit 命令
      if (command === 'commit') {
        if (message) {
          gitCommand += ` -m "${message}"`;
        }
        if (options.length > 0) {
          gitCommand += ` ${options.join(' ')}`;
        }
      } else {
        // 其他命令直接拼接选项
        if (options.length > 0) {
          gitCommand += ` ${options.join(' ')}`;
        }
      }
      
      console.log(`执行命令: ${gitCommand}`);
      
      const output = execSync(gitCommand, { 
        encoding: 'utf8',
        cwd: process.cwd(),
        timeout: 30000 // 30秒超时
      });
      
      return {
        success: true,
        command: gitCommand,
        output: output.trim()
      };
    } catch (error) {
      const errorMessage = error instanceof Error ? error.message : '未知错误';
      return {
        success: false,
        command: `git ${command}`,
        error: errorMessage,
        output: ''
      };
    }
  }
};

// 导出其他常用 git 工具的便捷函数
export const createGitStatusTool = (): Tool => ({
  name: 'git_status',
  description: '查看 Git 仓库当前状态，显示已修改、已暂存和未跟踪的文件',
  parameters: {
    type: 'object',
    properties: {},
    required: []
  },
  execute: async () => {
    return await gitTool.execute({ command: 'status', options: ['--porcelain'] });
  }
});

export const createGitCommitTool = (): Tool => ({
  name: 'git_commit',
  description: '提交暂存区的更改到 Git 仓库',
  parameters: {
    type: 'object',
    properties: {
      message: {
        type: 'string',
        description: '提交信息，描述本次更改的内容'
      },
      addAll: {
        type: 'boolean',
        description: '是否先执行 git add . 添加所有更改'
      }
    },
    required: ['message']
  },
  execute: async (args: Record<string, any>) => {
    const { message, addAll } = args;
    
    try {
      const results = [];
      
      // 如果需要，先添加所有文件
      if (addAll) {
        const addResult = await gitTool.execute({ command: 'add', options: ['.'] });
        results.push(addResult);
      }
      
      // 执行提交
      const commitResult = await gitTool.execute({ command: 'commit', message });
      results.push(commitResult);
      
      return {
        success: results.every(r => r.success),
        steps: results
      };
    } catch (error) {
      return {
        success: false,
        error: error instanceof Error ? error.message : '提交失败'
      };
    }
  }
});