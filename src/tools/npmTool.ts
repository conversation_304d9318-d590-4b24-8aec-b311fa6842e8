import { execSync } from 'child_process';
import { existsSync, readFileSync } from 'fs';
import { join } from 'path';
import { Tool } from '../types';

export const npmTool: Tool = {
  name: 'npm',
  description: '执行 NPM 命令来管理项目依赖和运行脚本。支持 install, run, test, build, audit 等常用操作。',
  parameters: {
    type: 'object',
    properties: {
      command: {
        type: 'string',
        description: 'NPM 子命令',
        enum: ['install', 'run', 'test', 'build', 'start', 'audit', 'update', 'outdated', 'list', 'init', 'publish']
      },
      script: {
        type: 'string',
        description: '要运行的脚本名称（仅用于 npm run 命令）'
      },
      packages: {
        type: 'array',
        description: '要安装的包名列表（仅用于 install 命令）'
      },
      options: {
        type: 'array',
        description: '额外的命令选项，如 --save-dev, --global 等'
      },
      workingDir: {
        type: 'string',
        description: '执行命令的工作目录（默认为当前目录）'
      }
    },
    required: ['command']
  },
  execute: async (args: Record<string, any>) => {
    const { command, script, packages = [], options = [], workingDir = process.cwd() } = args;
    
    try {
      let npmCommand = 'npm';
      
      // 构建命令
      switch (command) {
        case 'run':
          if (!script) {
            return {
              success: false,
              error: 'npm run 命令需要指定 script 参数',
              command: 'npm run'
            };
          }
          npmCommand += ` run ${script}`;
          break;
          
        case 'install':
          npmCommand += ' install';
          if (packages.length > 0) {
            npmCommand += ` ${packages.join(' ')}`;
          }
          break;
          
        default:
          npmCommand += ` ${command}`;
      }
      
      // 添加选项
      if (options.length > 0) {
        npmCommand += ` ${options.join(' ')}`;
      }
      
      console.log(`执行 NPM 命令: ${npmCommand}`);
      console.log(`工作目录: ${workingDir}`);
      
      const output = execSync(npmCommand, { 
        encoding: 'utf8',
        cwd: workingDir,
        timeout: 120000, // 2分钟超时，npm 操作可能比较慢
        stdio: 'pipe'
      });
      
      return {
        success: true,
        command: npmCommand,
        output: output.trim(),
        workingDir
      };
    } catch (error: any) {
      // NPM 错误通常包含有用的信息
      const errorOutput = error.stderr ? error.stderr.toString() : error.message;
      return {
        success: false,
        command: `npm ${command}`,
        error: errorOutput,
        workingDir,
        exitCode: error.status
      };
    }
  }
};

export const packageJsonAnalyzerTool: Tool = {
  name: 'analyze_package_json',
  description: '分析项目的 package.json 文件，识别可用的脚本、依赖信息和项目配置。',
  parameters: {
    type: 'object',
    properties: {
      projectPath: {
        type: 'string',
        description: '项目根目录路径（默认为当前目录）'
      },
      includeDevDeps: {
        type: 'boolean',
        description: '是否包含开发依赖信息'
      }
    },
    required: []
  },
  execute: async (args: Record<string, any>) => {
    const { projectPath = process.cwd(), includeDevDeps = true } = args;
    
    try {
      const packageJsonPath = join(projectPath, 'package.json');
      
      if (!existsSync(packageJsonPath)) {
        return {
          success: false,
          error: `在 ${projectPath} 目录下没有找到 package.json 文件`,
          path: packageJsonPath
        };
      }
      
      const packageJsonContent = readFileSync(packageJsonPath, 'utf8');
      const packageJson = JSON.parse(packageJsonContent);
      
      // 分析脚本
      const scripts = packageJson.scripts || {};
      const availableScripts = Object.keys(scripts);
      
      // 分析依赖
      const dependencies = packageJson.dependencies || {};
      const devDependencies = includeDevDeps ? (packageJson.devDependencies || {}) : {};
      
      // 分析项目信息
      const projectInfo = {
        name: packageJson.name,
        version: packageJson.version,
        description: packageJson.description,
        main: packageJson.main,
        engines: packageJson.engines,
        author: packageJson.author,
        license: packageJson.license
      };
      
      // 识别常用脚本类型
      const scriptCategories = categorizeScripts(scripts);
      
      return {
        success: true,
        path: packageJsonPath,
        projectInfo,
        scripts: {
          available: availableScripts,
          definitions: scripts,
          categories: scriptCategories
        },
        dependencies: {
          production: Object.keys(dependencies),
          development: Object.keys(devDependencies),
          total: Object.keys(dependencies).length + Object.keys(devDependencies).length
        },
        recommendations: generateRecommendations(scripts, dependencies, devDependencies)
      };
    } catch (error) {
      return {
        success: false,
        error: error instanceof Error ? error.message : '分析 package.json 失败',
        path: join(projectPath, 'package.json')
      };
    }
  }
};

export const smartNpmTool: Tool = {
  name: 'smart_npm',
  description: '智能 NPM 操作工具：自动分析项目并建议合适的 NPM 命令。支持智能脚本识别和依赖管理。',
  parameters: {
    type: 'object',
    properties: {
      intent: {
        type: 'string',
        description: '操作意图',
        enum: ['build', 'test', 'dev', 'start', 'install', 'update', 'clean', 'analyze', 'audit']
      },
      target: {
        type: 'string',
        description: '目标内容（如包名、脚本名等）'
      },
      projectPath: {
        type: 'string',
        description: '项目路径（默认当前目录）'
      },
      dryRun: {
        type: 'boolean',
        description: '是否只分析不执行'
      }
    },
    required: ['intent']
  },
  execute: async (args: Record<string, any>) => {
    const { intent, target, projectPath = process.cwd(), dryRun = false } = args;
    
    try {
      // 首先分析 package.json
      const analysis = await packageJsonAnalyzerTool.execute({ projectPath });
      
      if (!analysis.success) {
        return {
          success: false,
          error: ' 无法分析项目 package.json: ' + analysis.error,
          intent
        };
      }
      
      // 根据意图智能选择命令
      const recommendation = smartRecommendCommand(intent, target, analysis);
      
      if (dryRun) {
        return {
          success: true,
          intent,
          recommendation,
          analysis,
          dryRun: true,
          message: `建议执行: ${recommendation.command}`,
          reasoning: recommendation.reasoning
        };
      }
      
      // 执行推荐的命令
      if (recommendation.npmCommand) {
        const result = await npmTool.execute({
          command: recommendation.npmCommand.command,
          script: recommendation.npmCommand.script,
          packages: recommendation.npmCommand.packages,
          options: recommendation.npmCommand.options || [],
          workingDir: projectPath
        });
        
        return {
          success: result.success,
          intent,
          command: recommendation.command,
          reasoning: recommendation.reasoning,
          output: result.output,
          error: result.error,
          executed: true
        };
      } else {
        return {
          success: false,
          intent,
          error: recommendation.error || '无法确定要执行的命令',
          recommendation
        };
      }
      
    } catch (error) {
      return {
        success: false,
        intent,
        error: error instanceof Error ? error.message : '智能 NPM 操作失败'
      };
    }
  }
};

// === 辅助函数 ===

function categorizeScripts(scripts: Record<string, string>) {
  const categories = {
    build: [] as string[],
    test: [] as string[],
    dev: [] as string[],
    start: [] as string[],
    lint: [] as string[],
    clean: [] as string[],
    deploy: [] as string[],
    other: [] as string[]
  };
  
  for (const [name, command] of Object.entries(scripts)) {
    const lowerName = name.toLowerCase();
    const lowerCommand = command.toLowerCase();
    
    if (lowerName.includes('build') || lowerCommand.includes('build')) {
      categories.build.push(name);
    } else if (lowerName.includes('test') || lowerCommand.includes('test')) {
      categories.test.push(name);
    } else if (lowerName.includes('dev') || lowerName.includes('watch') || lowerCommand.includes('watch')) {
      categories.dev.push(name);
    } else if (lowerName.includes('start') || lowerName.includes('serve')) {
      categories.start.push(name);
    } else if (lowerName.includes('lint') || lowerCommand.includes('lint')) {
      categories.lint.push(name);
    } else if (lowerName.includes('clean') || lowerCommand.includes('clean')) {
      categories.clean.push(name);
    } else if (lowerName.includes('deploy') || lowerCommand.includes('deploy')) {
      categories.deploy.push(name);
    } else {
      categories.other.push(name);
    }
  }
  
  return categories;
}

function generateRecommendations(
  scripts: Record<string, string>, 
  dependencies: Record<string, string>, 
  devDependencies: Record<string, string>
) {
  const recommendations = [];
  
  // 检查是否有过时的依赖
  recommendations.push('运行 npm audit 检查安全漏洞');
  recommendations.push('运行 npm outdated 检查过时依赖');
  
  // 基于脚本推荐
  if (scripts.test) {
    recommendations.push('使用 npm test 运行测试');
  }
  if (scripts.build) {
    recommendations.push('使用 npm run build 构建项目');
  }
  if (scripts.dev || scripts.develop) {
    recommendations.push('使用 npm run dev 启动开发模式');
  }
  if (scripts.start) {
    recommendations.push('使用 npm start 启动应用');
  }
  
  return recommendations;
}

function smartRecommendCommand(intent: string, target: string | undefined, analysis: any) {
  const scripts = analysis.scripts.definitions;
  const categories = analysis.scripts.categories;
  
  switch (intent) {
    case 'build':
      if (target && scripts[target]) {
        return {
          command: `npm run ${target}`,
          npmCommand: { command: 'run', script: target, options: [] },
          reasoning: `用户指定运行脚本: ${target}`
        };
      }
      if (categories.build.length > 0) {
        const buildScript = categories.build[0];
        return {
          command: `npm run ${buildScript}`,
          npmCommand: { command: 'run', script: buildScript, options: [] },
          reasoning: `找到构建脚本: ${buildScript}`
        };
      }
      return {
        command: 'npm run build',
        error: '项目中没有找到构建脚本',
        reasoning: '尝试默认的 build 脚本'
      };
      
    case 'test':
      if (target && scripts[target]) {
        return {
          command: `npm run ${target}`,
          npmCommand: { command: 'run', script: target, options: [] },
          reasoning: `用户指定运行测试脚本: ${target}`
        };
      }
      if (scripts.test) {
        return {
          command: 'npm test',
          npmCommand: { command: 'test', options: [] },
          reasoning: '找到标准测试脚本'
        };
      }
      if (categories.test.length > 0) {
        const testScript = categories.test[0];
        return {
          command: `npm run ${testScript}`,
          npmCommand: { command: 'run', script: testScript, options: [] },
          reasoning: `找到测试脚本: ${testScript}`
        };
      }
      return {
        command: 'npm test',
        error: '项目中没有找到测试脚本',
        reasoning: '尝试默认的 test 命令'
      };
      
    case 'dev':
      if (target && scripts[target]) {
        return {
          command: `npm run ${target}`,
          npmCommand: { command: 'run', script: target, options: [] },
          reasoning: `用户指定运行开发脚本: ${target}`
        };
      }
      if (categories.dev.length > 0) {
        const devScript = categories.dev[0];
        return {
          command: `npm run ${devScript}`,
          npmCommand: { command: 'run', script: devScript, options: [] },
          reasoning: `找到开发脚本: ${devScript}`
        };
      }
      if (scripts.start) {
        return {
          command: 'npm start',
          npmCommand: { command: 'start', options: [] },
          reasoning: '使用 start 脚本作为开发启动'
        };
      }
      return {
        command: 'npm run dev',
        error: '项目中没有找到开发脚本',
        reasoning: '尝试默认的 dev 脚本'
      };
      
    case 'start':
      if (scripts.start) {
        return {
          command: 'npm start',
          npmCommand: { command: 'start', options: [] },
          reasoning: '找到 start 脚本'
        };
      }
      return {
        command: 'npm start',
        error: '项目中没有找到 start 脚本',
        reasoning: '尝试默认的 start 命令'
      };
      
    case 'install':
      if (target) {
        return {
          command: `npm install ${target}`,
          npmCommand: { command: 'install', packages: [target], options: [] },
          reasoning: `安装指定的包: ${target}`
        };
      }
      return {
        command: 'npm install',
        npmCommand: { command: 'install', options: [] },
        reasoning: '安装所有依赖'
      };
      
    case 'update':
      if (target) {
        return {
          command: `npm update ${target}`,
          npmCommand: { command: 'update', packages: [target], options: [] },
          reasoning: `更新指定的包: ${target}`
        };
      }
      return {
        command: 'npm update',
        npmCommand: { command: 'update', options: [] },
        reasoning: '更新所有依赖'
      };
      
    case 'audit':
      return {
        command: 'npm audit',
        npmCommand: { command: 'audit', options: [] },
        reasoning: '检查依赖安全漏洞'
      };
      
    case 'clean':
      if (categories.clean.length > 0) {
        const cleanScript = categories.clean[0];
        return {
          command: `npm run ${cleanScript}`,
          npmCommand: { command: 'run', script: cleanScript, options: [] },
          reasoning: `找到清理脚本: ${cleanScript}`
        };
      }
      return {
        command: 'rm -rf node_modules && npm install',
        error: '项目中没有找到清理脚本，建议手动清理',
        reasoning: '手动清理 node_modules 并重新安装'
      };
      
    case 'analyze':
      return {
        command: 'package.json 分析完成',
        reasoning: '项目分析结果已返回',
        analysis: true
      };
      
    default:
      return {
        command: `npm ${intent}`,
        error: `未知的操作意图: ${intent}`,
        reasoning: '尝试直接执行 npm 命令'
      };
  }
}