import { execSync } from 'child_process';
import { Tool } from '../types';
import { SmartCommitAnalyzer } from './smartCommitAnalyzer';

export const gitDiffTool: Tool = {
  name: 'git_diff',
  description: '获取 Git 仓库的变更差异，可以获取暂存区或工作目录的变更内容',
  parameters: {
    type: 'object',
    properties: {
      staged: {
        type: 'boolean',
        description: '是否获取暂存区的变更（true）还是工作目录的变更（false）'
      },
      files: {
        type: 'array',
        description: '指定要查看差异的文件列表（可选）'
      },
      stat: {
        type: 'boolean',
        description: '是否只显示变更统计信息而不是详细内容'
      }
    },
    required: []
  },
  execute: async (args: Record<string, any>) => {
    const { staged = true, files = [], stat = false } = args;
    
    try {
      let gitCommand = 'git diff';
      
      // 选择比较的范围
      if (staged) {
        gitCommand += ' --cached'; // 暂存区的变更
      }
      
      // 是否只显示统计信息
      if (stat) {
        gitCommand += ' --stat';
      }
      
      // 添加特定文件
      if (files.length > 0) {
        gitCommand += ` -- ${files.join(' ')}`;
      }
      
      console.log(`执行命令: ${gitCommand}`);
      
      const output = execSync(gitCommand, { 
        encoding: 'utf8',
        cwd: process.cwd(),
        timeout: 30000
      });
      
      return {
        success: true,
        command: gitCommand,
        diff: output.trim(),
        hasDiff: output.trim().length > 0
      };
    } catch (error) {
      const errorMessage = error instanceof Error ? error.message : '未知错误';
      return {
        success: false,
        command: `git diff`,
        error: errorMessage,
        diff: '',
        hasDiff: false
      };
    }
  }
};

export const generateCommitMessageTool: Tool = {
  name: 'generate_commit_message',
  description: '基于 git diff 智能生成符合规范的 commit 消息。会分析代码变更内容，生成语义化的提交信息。',
  parameters: {
    type: 'object',
    properties: {
      diffContent: {
        type: 'string',
        description: 'git diff 的输出内容，用于分析变更'
      },
      conventional: {
        type: 'boolean',
        description: '是否使用 Conventional Commits 规范（默认 true）'
      },
      language: {
        type: 'string',
        description: 'commit 消息的语言',
        enum: ['zh', 'en']
      }
    },
    required: ['diffContent']
  },
  execute: async (args: Record<string, any>) => {
    const { diffContent, conventional = true, language = 'zh' } = args;
    
    if (!diffContent || diffContent.trim().length === 0) {
      return {
        success: false,
        error: '没有检测到任何变更内容',
        message: ''
      };
    }

    try {
      // 使用智能分析器
      const analysis = SmartCommitAnalyzer.analyzeDiff(diffContent);
      
      // 生成 commit 消息
      const message = generateSmartMessage(analysis, conventional, language);
      
      return {
        success: true,
        message,
        analysis,
        type: analysis.primaryType,
        scope: analysis.scope,
        confidence: analysis.confidence,
        reasoning: analysis.reasoning
      };
    } catch (error) {
      return {
        success: false,
        error: error instanceof Error ? error.message : '生成 commit 消息失败',
        message: ''
      };
    }
  }
};

export const smartCommitTool: Tool = {
  name: 'smart_commit',
  description: '智能提交工具：自动分析 git diff，生成合适的 commit 消息并执行提交。支持自动添加文件到暂存区。',
  parameters: {
    type: 'object',
    properties: {
      addAll: {
        type: 'boolean',
        description: '是否先执行 git add . 添加所有更改到暂存区'
      },
      customMessage: {
        type: 'string',
        description: '自定义 commit 消息（如果提供，则跳过自动生成）'
      },
      dryRun: {
        type: 'boolean',
        description: '是否只生成消息而不实际提交'
      },
      conventional: {
        type: 'boolean',
        description: '是否使用 Conventional Commits 规范'
      }
    },
    required: []
  },
  execute: async (args: Record<string, any>) => {
    const { addAll = false, customMessage, dryRun = false, conventional = true } = args;
    
    const steps: any[] = [];
    
    try {
      
      // Step 1: 如果需要，添加所有文件
      if (addAll) {
        const addResult = execSync('git add .', { encoding: 'utf8', cwd: process.cwd() });
        steps.push({
          step: 'add',
          command: 'git add .',
          success: true,
          output: 'Files added to staging area'
        });
      }
      
      // Step 2: 获取暂存区的变更
      const diffResult = await gitDiffTool.execute({ staged: true });
      steps.push({
        step: 'diff',
        success: diffResult.success,
        hasDiff: diffResult.hasDiff,
        diffLength: diffResult.diff?.length || 0
      });
      
      if (!diffResult.success || !diffResult.hasDiff) {
        return {
          success: false,
          error: '暂存区没有变更需要提交',
          steps
        };
      }
      
      // Step 3: 生成或使用自定义 commit 消息
      let commitMessage = customMessage;
      let analysis = null;
      
      if (!commitMessage) {
        const msgResult = await generateCommitMessageTool.execute({
          diffContent: diffResult.diff,
          conventional,
          language: 'zh'
        });
        
        steps.push({
          step: 'generate_message',
          success: msgResult.success,
          analysis: msgResult.analysis
        });
        
        if (!msgResult.success) {
          return {
            success: false,
            error: '生成 commit 消息失败: ' + msgResult.error,
            steps
          };
        }
        
        commitMessage = msgResult.message;
        analysis = msgResult.analysis;
      }
      
      // Step 4: 执行提交（或干运行）
      if (dryRun) {
        steps.push({
          step: 'dry_run',
          message: commitMessage,
          wouldCommit: true
        });
        
        return {
          success: true,
          message: commitMessage,
          analysis,
          dryRun: true,
          steps
        };
      } else {
        const commitResult = execSync(`git commit -m "${commitMessage.replace(/"/g, '\\"')}"`, {
          encoding: 'utf8',
          cwd: process.cwd()
        });
        
        steps.push({
          step: 'commit',
          command: `git commit -m "${commitMessage}"`,
          success: true,
          output: commitResult.trim()
        });
        
        return {
          success: true,
          message: commitMessage,
          analysis,
          committed: true,
          steps
        };
      }
      
    } catch (error) {
      return {
        success: false,
        error: error instanceof Error ? error.message : '智能提交失败',
        steps
      };
    }
  }
};

// 新的智能消息生成函数
function generateSmartMessage(analysis: any, conventional: boolean, language: string): string {
  if (!conventional) {
    // 简单格式，但更智能
    return analysis.description;
  }
  
  // Conventional Commits 格式，但更精确
  let message = analysis.primaryType;
  
  // 只有当 scope 真正有意义时才添加
  if (analysis.scope && analysis.scope !== 'src' && analysis.scope !== 'core') {
    message += `(${analysis.scope})`;
  }
  
  if (analysis.isBreakingChange) {
    message += '!';
  }
  
  message += `: ${analysis.description}`;
  
  // 如果置信度较低，添加更多上下文
  if (analysis.confidence < 0.7 && analysis.files.length <= 2) {
    const fileNames = analysis.files.map((f: string) => {
      const name = f.split('/').pop();
      return name?.replace(/\.(ts|js|tsx|jsx)$/, '') || name;
    }).join(', ');
    
    if (fileNames && !message.includes(fileNames)) {
      message += ` (${fileNames})`;
    }
  }
  
  return message;
}

// 保留原来的函数作为后备（但改进了一些逻辑）
// 分析变更内容的辅助函数
function analyzeChanges(diffContent: string): {
  primaryType: string;
  scope: string | null;
  description: string;
  files: string[];
  additions: number;
  deletions: number;
  isBreakingChange: boolean;
} {
  const lines = diffContent.split('\n');
  const files: string[] = [];
  let additions = 0;
  let deletions = 0;
  
  // 解析文件和变更统计
  for (const line of lines) {
    if (line.startsWith('diff --git')) {
      const match = line.match(/diff --git a\/(.*) b\/(.*)/);
      if (match) {
        files.push(match[1]);
      }
    } else if (line.startsWith('+') && !line.startsWith('+++')) {
      additions++;
    } else if (line.startsWith('-') && !line.startsWith('---')) {
      deletions++;
    }
  }
  
  // 分析变更类型
  const analysis = {
    primaryType: 'feat',
    scope: null as string | null,
    description: '',
    files,
    additions,
    deletions,
    isBreakingChange: false
  };
  
  // 根据文件类型和变更内容判断类型
  const hasNewFiles = diffContent.includes('new file mode');
  const hasDeletedFiles = diffContent.includes('deleted file mode');
  const hasTestFiles = files.some(f => f.includes('test') || f.includes('spec'));
  const hasConfigFiles = files.some(f => f.includes('config') || f.includes('.json') || f.includes('.yml') || f.includes('.yaml'));
  const hasDocFiles = files.some(f => f.includes('.md') || f.includes('doc'));
  const hasTsFiles = files.some(f => f.includes('.ts') || f.includes('.js'));
  
  // 判断作用域
  if (files.length === 1) {
    const file = files[0];
    if (file.includes('/')) {
      analysis.scope = file.split('/')[0];
    }
  } else if (files.length > 0) {
    const commonPath = findCommonPath(files);
    if (commonPath) {
      analysis.scope = commonPath;
    }
  }
  
  // 判断变更类型
  if (hasTestFiles && !hasTsFiles) {
    analysis.primaryType = 'test';
    analysis.description = '添加或更新测试';
  } else if (hasDocFiles && !hasTsFiles) {
    analysis.primaryType = 'docs';
    analysis.description = '更新文档';
  } else if (hasConfigFiles && !hasTsFiles) {
    analysis.primaryType = 'chore';
    analysis.description = '更新配置';
  } else if (hasDeletedFiles) {
    analysis.primaryType = 'refactor';
    analysis.description = '重构代码';
  } else if (hasNewFiles) {
    analysis.primaryType = 'feat';
    analysis.description = '添加新功能';
  } else if (deletions > additions * 2) {
    analysis.primaryType = 'refactor';
    analysis.description = '重构代码';
  } else if (diffContent.includes('fix') || diffContent.includes('bug')) {
    analysis.primaryType = 'fix';
    analysis.description = '修复问题';
  } else {
    analysis.primaryType = 'feat';
    analysis.description = '功能改进';
  }
  
  // 检查是否为破坏性变更
  if (diffContent.includes('BREAKING CHANGE') || diffContent.includes('breaking')) {
    analysis.isBreakingChange = true;
  }
  
  return analysis;
}

// 生成 commit 消息
function generateMessage(analysis: any, conventional: boolean, language: string): string {
  if (!conventional) {
    // 简单格式
    return language === 'en' ? 
      `${analysis.description}` : 
      `${analysis.description}`;
  }
  
  // Conventional Commits 格式
  let message = analysis.primaryType;
  
  if (analysis.scope) {
    message += `(${analysis.scope})`;
  }
  
  if (analysis.isBreakingChange) {
    message += '!';
  }
  
  message += `: ${analysis.description}`;
  
  // 添加文件信息（如果只有少数几个文件）
  if (analysis.files.length <= 3 && analysis.files.length > 0) {
    const filesList = analysis.files.map((f: string) => f.split('/').pop()).join(', ');
    if (language === 'en') {
      message += ` (${filesList})`;
    } else {
      message += ` (${filesList})`;
    }
  }
  
  return message;
}

// 查找文件的公共路径
function findCommonPath(files: string[]): string | null {
  if (files.length === 0) return null;
  if (files.length === 1) {
    const parts = files[0].split('/');
    return parts.length > 1 ? parts[0] : null;
  }
  
  const parts = files[0].split('/');
  let commonPath = '';
  
  for (let i = 0; i < parts.length - 1; i++) {
    const part = parts[i];
    if (files.every(file => file.split('/')[i] === part)) {
      commonPath = commonPath ? `${commonPath}/${part}` : part;
    } else {
      break;
    }
  }
  
  return commonPath || null;
}