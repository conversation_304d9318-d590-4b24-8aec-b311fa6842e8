import { 
  Task, 
  TaskStatus, 
  TaskPriority,
  CreateTaskRequest,
  UpdateTaskRequest,
  TaskFilter,
  TaskComplexityAnalysis,
  TaskCategory,
  TaskLogEntry,
  TaskLogType
} from '../types/task';
import { TaskComplexityAnalyzer } from './taskComplexityAnalyzer';
import { TaskDecomposer } from './taskDecomposer';
import { TaskExecutor } from './taskExecutor';
import { ConfigManager } from './config';
import { MemoryManager } from './memory';
import { v4 as uuidv4 } from 'uuid';
import { existsSync, readFileSync, writeFileSync, mkdirSync } from 'fs';
import { join, dirname } from 'path';
import chalk from 'chalk';

export class TaskManager {
  private tasks: Map<string, Task> = new Map();
  private complexityAnalyzer: TaskComplexityAnalyzer;
  private taskDecomposer: TaskDecomposer;
  private taskExecutor: TaskExecutor;
  private memory: MemoryManager;
  private taskStorePath: string;

  constructor(private config: ConfigManager) {
    this.complexityAnalyzer = new TaskComplexityAnalyzer();
    this.taskDecomposer = new TaskDecomposer(config);
    this.taskExecutor = new TaskExecutor(config);
    this.memory = new MemoryManager();
    this.taskStorePath = join(process.cwd(), '.chater_tasks.json');
    
    this.loadTasks();
  }

  /**
   * 创建新任务
   */
  public async createTask(request: CreateTaskRequest): Promise<Task> {
    console.log(chalk.blue('🔍 分析任务复杂度...'));
    
    // 分析任务复杂度
    const complexityAnalysis = this.complexityAnalyzer.analyzeComplexity(
      request.description, 
      request.context
    );
    
    const category = this.complexityAnalyzer.analyzeTaskCategory(request.description);
    
    console.log(chalk.cyan(`📊 复杂度: ${complexityAnalysis.complexity} (${complexityAnalysis.score}/100)`));
    console.log(chalk.gray(`📋 类型: ${category}`));
    console.log(chalk.yellow(`💡 ${complexityAnalysis.recommendation}`));
    
    // 创建基础任务
    const task: Task = {
      id: uuidv4(),
      title: this.generateTaskTitle(request.description),
      description: request.description,
      complexity: complexityAnalysis.complexity,
      status: 'pending',
      priority: request.priority || 'medium',
      createdAt: new Date(),
      estimatedDuration: complexityAnalysis.estimatedTime,
      subtasks: [],
      currentStep: 0,
      totalSteps: 0,
      executionLog: [],
      context: {
        projectPath: request.context?.projectPath || process.cwd(),
        relevantFiles: request.context?.relevantFiles || [],
        environmentInfo: request.context?.environmentInfo || {},
        userPreferences: request.userPreferences || {},
        previousTasks: []
      },
      requiresUserInput: false,
      userNotes: []
    };
    
    // 添加创建日志
    task.executionLog.push(this.createLogEntry('info', '任务已创建', undefined, complexityAnalysis));
    
    // 如果需要分解，进行任务分解
    if (complexityAnalysis.requiresDecomposition) {
      console.log(chalk.blue('🧠 开始任务分解...'));
      task.status = 'planning';
      
      try {
        const executionPlan = await this.taskDecomposer.decomposeTask(
          request, 
          complexityAnalysis, 
          category
        );
        
        // 验证执行计划
        const validation = this.taskDecomposer.validateExecutionPlan(executionPlan);
        if (!validation.isValid) {
          console.log(chalk.red('❌ 执行计划验证失败:'));
          validation.issues.forEach(issue => console.log(chalk.red(`  - ${issue}`)));
          throw new Error('执行计划无效');
        }
        
        task.executionPlan = executionPlan;
        task.totalSteps = executionPlan.steps.length;
        task.status = 'ready';
        
        console.log(chalk.green(`✅ 任务分解完成，共 ${task.totalSteps} 个步骤`));
        
        // 显示执行计划摘要
        this.displayExecutionPlanSummary(executionPlan);
        
        task.executionLog.push(this.createLogEntry('success', '任务分解完成', undefined, executionPlan));
        
      } catch (error) {
        task.status = 'failed';
        const errorMessage = error instanceof Error ? error.message : '任务分解失败';
        task.executionLog.push(this.createLogEntry('error', errorMessage));
        console.error(chalk.red('❌ 任务分解失败:'), error);
      }
    } else {
      // 简单任务，直接准备执行
      task.status = 'ready';
      task.totalSteps = 1;
      console.log(chalk.green('✅ 简单任务，可直接执行'));
    }
    
    // 保存任务
    this.tasks.set(task.id, task);
    this.saveTasks();
    
    // 记录到项目记忆
    await this.recordTaskToMemory(task);
    
    return task;
  }

  /**
   * 执行任务
   */
  public async executeTask(taskId: string): Promise<{ success: boolean; result: string }> {
    const task = this.tasks.get(taskId);
    if (!task) {
      throw new Error(`任务不存在: ${taskId}`);
    }
    
    if (task.status !== 'ready') {
      throw new Error(`任务状态不允许执行: ${task.status}`);
    }
    
    console.log(chalk.blue(`\n🚀 开始执行任务: ${task.title}`));
    
    try {
      const result = await this.taskExecutor.executeTask(task);
      
      // 更新任务状态
      this.tasks.set(taskId, task);
      this.saveTasks();
      
      // 记录完成情况到记忆
      await this.recordTaskCompletionToMemory(task, result.success);
      
      return { success: result.success, result: result.finalResult };
      
    } catch (error) {
      task.status = 'failed';
      const errorMessage = error instanceof Error ? error.message : '任务执行失败';
      task.executionLog.push(this.createLogEntry('error', errorMessage));
      
      this.tasks.set(taskId, task);
      this.saveTasks();
      
      throw error;
    }
  }

  /**
   * 暂停任务
   */
  public pauseTask(taskId: string): void {
    const task = this.tasks.get(taskId);
    if (!task) {
      throw new Error(`任务不存在: ${taskId}`);
    }
    
    if (task.status === 'in_progress') {
      task.status = 'paused';
      task.executionLog.push(this.createLogEntry('info', '任务已暂停'));
      this.tasks.set(taskId, task);
      this.saveTasks();
      
      console.log(chalk.yellow(`⏸️  任务已暂停: ${task.title}`));
    }
  }

  /**
   * 恢复任务
   */
  public resumeTask(taskId: string): void {
    const task = this.tasks.get(taskId);
    if (!task) {
      throw new Error(`任务不存在: ${taskId}`);
    }
    
    if (task.status === 'paused') {
      task.status = 'in_progress';
      task.executionLog.push(this.createLogEntry('info', '任务已恢复'));
      this.tasks.set(taskId, task);
      this.saveTasks();
      
      console.log(chalk.green(`▶️  任务已恢复: ${task.title}`));
    }
  }

  /**
   * 取消任务
   */
  public cancelTask(taskId: string): void {
    const task = this.tasks.get(taskId);
    if (!task) {
      throw new Error(`任务不存在: ${taskId}`);
    }
    
    task.status = 'cancelled';
    task.executionLog.push(this.createLogEntry('info', '任务已取消'));
    this.tasks.set(taskId, task);
    this.saveTasks();
    
    console.log(chalk.red(`❌ 任务已取消: ${task.title}`));
  }

  /**
   * 更新任务
   */
  public updateTask(request: UpdateTaskRequest): Task {
    const task = this.tasks.get(request.taskId);
    if (!task) {
      throw new Error(`任务不存在: ${request.taskId}`);
    }
    
    if (request.status) {
      task.status = request.status;
    }
    
    if (request.userNotes) {
      task.userNotes.push(...request.userNotes);
    }
    
    if (request.context) {
      Object.assign(task.context, request.context);
    }
    
    task.executionLog.push(this.createLogEntry('info', '任务已更新'));
    this.tasks.set(request.taskId, task);
    this.saveTasks();
    
    return task;
  }

  /**
   * 获取任务
   */
  public getTask(taskId: string): Task | undefined {
    return this.tasks.get(taskId);
  }

  /**
   * 获取所有任务
   */
  public getAllTasks(): Task[] {
    return Array.from(this.tasks.values());
  }

  /**
   * 搜索和过滤任务
   */
  public filterTasks(filter: TaskFilter): Task[] {
    let tasks = this.getAllTasks();
    
    if (filter.status) {
      tasks = tasks.filter(task => filter.status!.includes(task.status));
    }
    
    if (filter.complexity) {
      tasks = tasks.filter(task => filter.complexity!.includes(task.complexity));
    }
    
    if (filter.priority) {
      tasks = tasks.filter(task => filter.priority!.includes(task.priority));
    }
    
    if (filter.searchText) {
      const searchLower = filter.searchText.toLowerCase();
      tasks = tasks.filter(task => 
        task.title.toLowerCase().includes(searchLower) ||
        task.description.toLowerCase().includes(searchLower)
      );
    }
    
    if (filter.dateRange) {
      tasks = tasks.filter(task => 
        task.createdAt >= filter.dateRange!.start &&
        task.createdAt <= filter.dateRange!.end
      );
    }
    
    return tasks;
  }

  /**
   * 获取任务统计信息
   */
  public getTaskStatistics(): {
    total: number;
    byStatus: Record<TaskStatus, number>;
    byComplexity: Record<string, number>;
    byPriority: Record<TaskPriority, number>;
    completionRate: number;
  } {
    const tasks = this.getAllTasks();
    const total = tasks.length;
    
    const byStatus = tasks.reduce((acc, task) => {
      acc[task.status] = (acc[task.status] || 0) + 1;
      return acc;
    }, {} as Record<TaskStatus, number>);
    
    const byComplexity = tasks.reduce((acc, task) => {
      acc[task.complexity] = (acc[task.complexity] || 0) + 1;
      return acc;
    }, {} as Record<string, number>);
    
    const byPriority = tasks.reduce((acc, task) => {
      acc[task.priority] = (acc[task.priority] || 0) + 1;
      return acc;
    }, {} as Record<TaskPriority, number>);
    
    const completed = byStatus['completed'] || 0;
    const completionRate = total > 0 ? (completed / total) * 100 : 0;
    
    return {
      total,
      byStatus,
      byComplexity,
      byPriority,
      completionRate: Math.round(completionRate)
    };
  }

  /**
   * 显示执行计划摘要
   */
  private displayExecutionPlanSummary(plan: any): void {
    console.log(chalk.blue('\n📋 执行计划摘要:'));
    console.log(chalk.cyan(`🎯 目标: ${plan.title}`));
    console.log(chalk.gray(`📝 描述: ${plan.description}`));
    console.log(chalk.yellow(`⏱️  预计时间: ${plan.estimatedTime} 分钟`));
    
    if (plan.dependencies.length > 0) {
      console.log(chalk.magenta(`🔗 依赖: ${plan.dependencies.join(', ')}`));
    }
    
    console.log(chalk.blue('\n📋 执行步骤:'));
    plan.steps.forEach((step: any, index: number) => {
      console.log(chalk.cyan(`  ${index + 1}. ${step.title}`));
      console.log(chalk.gray(`     ${step.description}`));
      if (step.toolCalls.length > 0) {
        console.log(chalk.magenta(`     工具: ${step.toolCalls.join(', ')}`));
      }
    });
    console.log();
  }

  /**
   * 生成任务标题
   */
  private generateTaskTitle(description: string): string {
    // 简化版：取描述的前30个字符作为标题
    const title = description.length > 30 ? 
      description.substring(0, 30) + '...' : 
      description;
    return title;
  }

  /**
   * 创建日志条目
   */
  private createLogEntry(
    type: TaskLogType, 
    message: string, 
    stepId?: string, 
    details?: any
  ): TaskLogEntry {
    return {
      id: uuidv4(),
      timestamp: new Date(),
      type,
      stepId,
      message,
      details
    };
  }

  /**
   * 记录任务到项目记忆
   */
  private async recordTaskToMemory(task: Task): Promise<void> {
    try {
      const memoryEntry = `任务创建: ${task.title}\n` +
        `描述: ${task.description}\n` +
        `复杂度: ${task.complexity}\n` +
        `预估时间: ${task.estimatedDuration} 分钟\n` +
        `步骤数: ${task.totalSteps}`;
      
      await this.memory.addInsight(memoryEntry);
    } catch (error) {
      console.warn('记录任务到记忆失败:', error);
    }
  }

  /**
   * 记录任务完成情况到记忆
   */
  private async recordTaskCompletionToMemory(task: Task, success: boolean): Promise<void> {
    try {
      const duration = task.completedAt && task.startedAt ? 
        Math.round((task.completedAt.getTime() - task.startedAt.getTime()) / 60000) : 0;
      
      const memoryEntry = `任务${success ? '完成' : '失败'}: ${task.title}\n` +
        `实际用时: ${duration} 分钟\n` +
        `执行步骤: ${task.currentStep + 1}/${task.totalSteps}\n` +
        `状态: ${task.status}`;
      
      await this.memory.addInsight(memoryEntry);
    } catch (error) {
      console.warn('记录任务完成情况到记忆失败:', error);
    }
  }

  /**
   * 保存任务到文件
   */
  private saveTasks(): void {
    try {
      const tasksData = Array.from(this.tasks.values());
      const dir = dirname(this.taskStorePath);
      
      if (!existsSync(dir)) {
        mkdirSync(dir, { recursive: true });
      }
      
      writeFileSync(this.taskStorePath, JSON.stringify(tasksData, null, 2));
    } catch (error) {
      console.warn('保存任务失败:', error);
    }
  }

  /**
   * 从文件加载任务
   */
  private loadTasks(): void {
    try {
      if (existsSync(this.taskStorePath)) {
        const data = readFileSync(this.taskStorePath, 'utf8');
        const tasksData: Task[] = JSON.parse(data);
        
        tasksData.forEach(task => {
          // 恢复日期对象
          task.createdAt = new Date(task.createdAt);
          if (task.startedAt) task.startedAt = new Date(task.startedAt);
          if (task.completedAt) task.completedAt = new Date(task.completedAt);
          
          task.executionLog.forEach(log => {
            log.timestamp = new Date(log.timestamp);
          });
          
          this.tasks.set(task.id, task);
        });
        
        console.log(chalk.green(`📂 已加载 ${tasksData.length} 个历史任务`));
      }
    } catch (error) {
      console.warn('加载历史任务失败:', error);
    }
  }
}