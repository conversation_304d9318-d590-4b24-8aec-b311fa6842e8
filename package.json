{"name": "chater", "version": "0.3.3", "description": "An intelligent AI chat agent CLI tool powered by LangChain", "main": "dist/index.js", "bin": {"chater": "dist/cli.js"}, "scripts": {"build": "tsc", "dev": "NODE_OPTIONS=\"--no-deprecation\" LANGCHAIN_VERBOSE=false LANGCHAIN_TRACING_V2=false ts-node src/cli.ts", "start": "NODE_OPTIONS=\"--no-deprecation\" LANGCHAIN_VERBOSE=false LANGCHAIN_TRACING_V2=false node dist/cli.js", "start:dev": "node dist/cli.js", "test": "jest", "lint": "eslint src/**/*.ts", "prepare": "npm run build"}, "keywords": ["ai", "chat", "cli", "langchain", "assistant", "openai", "claude"], "author": "<EMAIL>", "license": "MIT", "dependencies": {"@langchain/anthropic": "^0.3.8", "@langchain/google-genai": "^0.1.8", "@langchain/openai": "^0.3.15", "@types/glob": "^8.1.0", "chalk": "^5.3.0", "commander": "^12.1.0", "cosmiconfig": "^9.0.0", "dotenv": "^16.4.7", "glob": "^11.0.3", "inquirer": "^12.0.0", "langchain": "^0.3.7", "ora": "^8.1.1", "uuid": "^11.0.3"}, "devDependencies": {"@types/inquirer": "^9.0.7", "@types/node": "^22.9.0", "@types/uuid": "^10.0.0", "@typescript-eslint/eslint-plugin": "^8.15.0", "@typescript-eslint/parser": "^8.15.0", "eslint": "^9.15.0", "jest": "^29.7.0", "ts-node": "^10.9.2", "typescript": "^5.7.2", "typescript-eslint": "^8.15.0"}, "engines": {"node": ">=18.0.0"}, "files": ["dist/**/*", "README.md"], "overrides": {"punycode": "^2.3.1"}}