import { ChatManager } from './chat';
import { ConfigManager } from './config';
import { ContextManager } from '../utils/contextManager';
import { ToolManager } from './toolManager';
import { 
  Task, 
  TaskStep, 
  TaskStatus, 
  TaskLogEntry, 
  TaskLogType,
  TaskValidationResult
} from '../types/task';
import { v4 as uuidv4 } from 'uuid';
import chalk from 'chalk';
import ora from 'ora';

export class TaskExecutor {
  private chatManager: ChatManager;
  private toolManager: ToolManager;

  constructor(config: ConfigManager) {
    this.chatManager = new ChatManager(config);
    this.toolManager = new ToolManager();
  }

  /**
   * 执行任务步骤
   */
  public async executeStep(
    step: TaskStep, 
    task: Task,
    context?: any
  ): Promise<{ success: boolean; result: string; logs: TaskLogEntry[] }> {
    
    const logs: TaskLogEntry[] = [];
    
    // 记录开始执行
    logs.push(this.createLogEntry('info', `开始执行步骤: ${step.title}`, step.id));
    
    const spinner = ora(`💻 执行步骤: ${step.title}`).start();
    
    try {
      // 更新步骤状态
      step.status = 'in_progress';
      step.startedAt = new Date();
      
      // 构建和优化执行提示词
      const basePrompt = this.buildExecutionPrompt(step, task, context);
      const { optimizedPrompt, warnings } = ContextManager.optimizePrompt(basePrompt, 'deepseek-chat');
      
      // 显示上下文优化警告
      if (warnings.length > 0) {
        spinner.stop();
        console.log(chalk.yellow('⚠️  步骤上下文已优化:'));
        warnings.forEach(warning => console.log(chalk.gray(`   ${warning}`)));
        spinner.start(`💻 执行步骤: ${step.title}`);
      }
      
      // 使用 deepseek-chat 执行步骤
      const result = await this.chatManager.sendMessage(optimizedPrompt, 'deepseek-chat');
      
      spinner.stop();
      console.log(chalk.green(`✅ 步骤完成: ${step.title}`));
      
      // 解析执行结果
      const executionResult = await this.processExecutionResult(result.response, step, logs);
      
      // 验证执行结果
      const validation = await this.validateStepResult(step, executionResult.output, task);
      step.validationResult = validation;
      
      if (validation.success) {
        step.status = 'completed';
        step.completedAt = new Date();
        step.actualOutput = executionResult.output;
        
        logs.push(this.createLogEntry('success', `步骤执行成功`, step.id));
        logs.push(this.createLogEntry('info', `验证分数: ${validation.score}/100`, step.id));
        
        return { success: true, result: executionResult.output, logs };
      } else {
        step.status = 'failed';
        logs.push(this.createLogEntry('error', `步骤执行失败: ${validation.details}`, step.id));
        
        return { success: false, result: validation.details, logs };
      }
      
    } catch (error) {
      spinner.stop();
      step.status = 'failed';
      
      const errorMessage = error instanceof Error ? error.message : '未知错误';
      console.error(chalk.red(`❌ 步骤执行失败: ${step.title}`), errorMessage);
      
      logs.push(this.createLogEntry('error', `步骤执行异常: ${errorMessage}`, step.id));
      
      return { success: false, result: errorMessage, logs };
    }
  }

  /**
   * 构建步骤执行的提示词（优化版）
   */
  private buildExecutionPrompt(step: TaskStep, task: Task, context?: any): string {
    // 构建简化的上下文信息
    const contextInfo = this.buildOptimizedContextInfo(task, context);
    
    return `执行步骤：${step.title}

任务：${task.title}
步骤：${task.currentStep + 1}/${task.totalSteps}

描述：${step.description}
类型：${step.type}
预期：${step.expectedOutput}
标准：${step.successCriteria.slice(0, 2).join('; ')}${step.successCriteria.length > 2 ? '...' : ''}

${step.toolCalls.length > 0 ? `工具：${step.toolCalls.join(', ')}` : ''}

${contextInfo}

输出格式：
### 执行过程
[操作步骤]

### 执行结果  
[最终结果]

### 验证
[验证方法]

开始执行：`;
  }

  /**
   * 构建上下文信息
   */
  private buildContextInfo(task: Task, context?: any): string {
    const parts = [];
    
    if (task.context.projectPath) {
      parts.push(`项目路径: ${task.context.projectPath}`);
    }
    
    if (task.context.relevantFiles.length > 0) {
      parts.push(`相关文件: ${task.context.relevantFiles.join(', ')}`);
    }
    
    if (task.executionLog.length > 0) {
      const recentLogs = task.executionLog.slice(-3);
      parts.push(`最近执行记录:\n${recentLogs.map(log => 
        `- ${log.type}: ${log.message}`
      ).join('\n')}`);
    }
    
    if (context) {
      parts.push(`额外上下文: ${JSON.stringify(context, null, 2)}`);
    }
    
    return parts.join('\n\n');
  }

  /**
   * 构建优化的上下文信息（减少token使用）
   */
  private buildOptimizedContextInfo(task: Task, context?: any): string {
    const parts = [];
    
    // 只保留关键文件（前3个）
    if (task.context.relevantFiles.length > 0) {
      const files = task.context.relevantFiles.slice(0, 3);
      const more = task.context.relevantFiles.length > 3 ? `等${task.context.relevantFiles.length}个文件` : '';
      parts.push(`文件: ${files.join(', ')}${more}`);
    }
    
    // 只保留最近1条日志
    if (task.executionLog.length > 0) {
      const lastLog = task.executionLog[task.executionLog.length - 1];
      parts.push(`上次: ${lastLog.type} - ${lastLog.message.substring(0, 50)}${lastLog.message.length > 50 ? '...' : ''}`);
    }
    
    return parts.length > 0 ? `上下文: ${parts.join(' | ')}` : '';
  }

  /**
   * 处理执行结果
   */
  private async processExecutionResult(
    aiResponse: string, 
    step: TaskStep, 
    logs: TaskLogEntry[]
  ): Promise<{ output: string; toolCalls: any[] }> {
    
    // 解析 AI 返回的结构化内容
    const executionResult = this.extractSection(aiResponse, '### 执行结果') || aiResponse;
    const toolCallsSection = this.extractSection(aiResponse, '### 工具调用');
    
    // 记录 AI 的执行过程
    const executionProcess = this.extractSection(aiResponse, '### 执行过程');
    if (executionProcess) {
      logs.push(this.createLogEntry('ai_reasoning', executionProcess, step.id, 'deepseek-chat'));
    }
    
    // 执行工具调用
    const toolResults = [];
    if (toolCallsSection && step.toolCalls.length > 0) {
      toolResults.push(...await this.executeToolCalls(toolCallsSection, step, logs));
    }
    
    return {
      output: executionResult,
      toolCalls: toolResults
    };
  }

  /**
   * 执行工具调用
   */
  private async executeToolCalls(
    toolCallsSection: string, 
    step: TaskStep, 
    logs: TaskLogEntry[]
  ): Promise<any[]> {
    
    const results = [];
    
    // 解析工具调用（简化版，实际应该更复杂）
    for (const toolName of step.toolCalls) {
      try {
        logs.push(this.createLogEntry('tool_call', `调用工具: ${toolName}`, step.id));
        
        // 这里应该根据 AI 的指示和工具名称执行具体的工具调用
        // 现在先简化处理
        const result = await this.executeSimulatedToolCall(toolName, toolCallsSection);
        
        results.push(result);
        logs.push(this.createLogEntry('info', `工具执行完成: ${toolName}`, step.id));
        
      } catch (error) {
        const errorMessage = error instanceof Error ? error.message : '工具执行失败';
        logs.push(this.createLogEntry('error', `工具执行失败: ${toolName} - ${errorMessage}`, step.id));
      }
    }
    
    return results;
  }

  /**
   * 模拟工具调用（简化版）
   */
  private async executeSimulatedToolCall(toolName: string, context: string): Promise<any> {
    // 这里应该调用实际的工具管理器
    // 现在返回模拟结果
    return {
      tool: toolName,
      success: true,
      result: `${toolName} 执行完成`,
      timestamp: new Date()
    };
  }

  /**
   * 验证步骤执行结果
   */
  private async validateStepResult(
    step: TaskStep, 
    result: string, 
    task: Task
  ): Promise<TaskValidationResult> {
    
    // 基础验证
    let score = 50; // 基础分数
    const issues = [];
    const suggestions = [];
    
    // 检查结果是否为空
    if (!result || result.trim().length === 0) {
      score -= 30;
      issues.push('执行结果为空');
    }
    
    // 检查是否提及了期望输出
    if (step.expectedOutput && !result.toLowerCase().includes(step.expectedOutput.toLowerCase())) {
      score -= 20;
      suggestions.push('结果与期望输出不完全匹配');
    }
    
    // 检查成功标准
    let criteriaMatched = 0;
    for (const criteria of step.successCriteria) {
      if (result.toLowerCase().includes(criteria.toLowerCase())) {
        criteriaMatched++;
        score += 10;
      }
    }
    
    if (criteriaMatched === 0 && step.successCriteria.length > 0) {
      score -= 25;
      issues.push('未满足任何成功标准');
    }
    
    // 检查错误指示词
    const errorIndicators = ['错误', '失败', '异常', 'error', 'failed', '无法'];
    const hasErrors = errorIndicators.some(indicator => 
      result.toLowerCase().includes(indicator)
    );
    
    if (hasErrors) {
      score -= 30;
      issues.push('结果中包含错误指示');
    }
    
    // 确保分数在合理范围内
    score = Math.max(0, Math.min(100, score));
    
    const success = score >= 60 && issues.length === 0;
    
    return {
      success,
      score,
      details: success ? 
        `步骤执行成功，满足 ${criteriaMatched}/${step.successCriteria.length} 个成功标准` :
        `步骤执行存在问题: ${issues.join(', ')}`,
      suggestions
    };
  }

  /**
   * 创建日志条目
   */
  private createLogEntry(
    type: TaskLogType, 
    message: string, 
    stepId?: string, 
    modelUsed?: string
  ): TaskLogEntry {
    return {
      id: uuidv4(),
      timestamp: new Date(),
      type,
      stepId,
      message,
      modelUsed
    };
  }

  /**
   * 提取响应中的特定章节
   */
  private extractSection(text: string, sectionTitle: string): string | null {
    const regex = new RegExp(`${sectionTitle}\\n([\\s\\S]*?)(?=\\n### |$)`);
    const match = text.match(regex);
    return match ? match[1].trim() : null;
  }

  /**
   * 执行整个任务的所有步骤
   */
  public async executeTask(task: Task): Promise<{ success: boolean; finalResult: string }> {
    console.log(chalk.blue(`🚀 开始执行任务: ${task.title}`));
    console.log(chalk.gray(`总共 ${task.totalSteps} 个步骤\n`));
    
    task.status = 'in_progress';
    task.startedAt = new Date();
    
    let allSuccess = true;
    let finalResult = '';
    
    for (let i = 0; i < task.executionPlan!.steps.length; i++) {
      const step = task.executionPlan!.steps[i];
      task.currentStep = i;
      
      console.log(chalk.cyan(`\n📋 步骤 ${i + 1}/${task.totalSteps}: ${step.title}`));
      
      // 检查依赖
      if (step.dependencies.length > 0) {
        const dependenciesMet = this.checkStepDependencies(step, task);
        if (!dependenciesMet) {
          console.log(chalk.red(`❌ 步骤依赖未满足: ${step.dependencies.join(', ')}`));
          allSuccess = false;
          break;
        }
      }
      
      // 执行步骤
      const result = await this.executeStep(step, task);
      
      // 添加日志到任务
      task.executionLog.push(...result.logs);
      
      if (!result.success) {
        console.log(chalk.red(`❌ 步骤失败: ${result.result}`));
        allSuccess = false;
        
        // 询问用户是否继续
        const shouldContinue = await this.askUserToContinue(step, result.result);
        if (!shouldContinue) {
          break;
        }
      } else {
        finalResult += result.result + '\n';
      }
    }
    
    // 更新任务状态
    task.status = allSuccess ? 'completed' : 'failed';
    task.completedAt = new Date();
    
    if (allSuccess) {
      console.log(chalk.green(`\n✅ 任务完成: ${task.title}`));
    } else {
      console.log(chalk.red(`\n❌ 任务失败: ${task.title}`));
    }
    
    return { success: allSuccess, finalResult };
  }

  /**
   * 检查步骤依赖
   */
  private checkStepDependencies(step: TaskStep, task: Task): boolean {
    if (!task.executionPlan) return true;
    
    for (const depTitle of step.dependencies) {
      const depStep = task.executionPlan.steps.find(s => s.title === depTitle);
      if (!depStep || depStep.status !== 'completed') {
        return false;
      }
    }
    
    return true;
  }

  /**
   * 询问用户是否继续执行
   */
  private async askUserToContinue(step: TaskStep, error: string): Promise<boolean> {
    console.log(chalk.yellow(`\n⚠️  步骤 "${step.title}" 执行失败`));
    console.log(chalk.red(`错误: ${error}`));
    console.log(chalk.gray('你可以选择继续执行后续步骤，或者停止任务。'));
    
    // 这里应该实现用户交互，现在简化为自动继续
    return false; // 暂时选择停止
  }
}