import chalk from 'chalk';

export interface AnimationOptions {
  duration?: number;
  interval?: number;
  color?: string;
  prefix?: string;
  suffix?: string;
}

export interface ThemeColors {
  primary: string;
  secondary: string;
  success: string;
  warning: string;
  error: string;
  info: string;
  muted: string;
  accent: string;
}

/**
 * 动画和视觉效果组件
 * 提供加载动画、过渡效果、颜色主题等
 */
export class Animations {
  private static themes: { [key: string]: ThemeColors } = {
    default: {
      primary: '#3b82f6',
      secondary: '#6b7280',
      success: '#10b981',
      warning: '#f59e0b',
      error: '#ef4444',
      info: '#06b6d4',
      muted: '#9ca3af',
      accent: '#8b5cf6'
    },
    dark: {
      primary: '#60a5fa',
      secondary: '#9ca3af',
      success: '#34d399',
      warning: '#fbbf24',
      error: '#f87171',
      info: '#22d3ee',
      muted: '#6b7280',
      accent: '#a78bfa'
    },
    light: {
      primary: '#2563eb',
      secondary: '#4b5563',
      success: '#059669',
      warning: '#d97706',
      error: '#dc2626',
      info: '#0891b2',
      muted: '#6b7280',
      accent: '#7c3aed'
    }
  };

  private static currentTheme: string = 'default';
  private static activeAnimations: Map<string, NodeJS.Timeout> = new Map();

  /**
   * 显示加载动画
   */
  public static showLoading(
    message: string = '处理中',
    options: AnimationOptions = {}
  ): string {
    const {
      duration = 0,
      interval = 200,
      color: _color = 'cyan',
      prefix = '',
      suffix = ''
    } = options;

    const frames = ['⠋', '⠙', '⠹', '⠸', '⠼', '⠴', '⠦', '⠧', '⠇', '⠏'];
    let frameIndex = 0;
    const animationId = Math.random().toString(36).substring(2, 11);

    const animation = setInterval(() => {
      process.stdout.write('\r' + ' '.repeat(80) + '\r'); // 清除当前行
      const frame = frames[frameIndex % frames.length];
      const coloredFrame = chalk.cyan(frame);
      const text = `${prefix}${coloredFrame} ${message}${suffix}`;
      process.stdout.write(text);
      frameIndex++;
    }, interval);

    this.activeAnimations.set(animationId, animation);

    if (duration > 0) {
      setTimeout(() => {
        this.stopAnimation(animationId);
      }, duration);
    }

    return animationId;
  }

  /**
   * 显示进度条动画
   */
  public static showProgress(
    current: number,
    total: number,
    message: string = '',
    width: number = 40
  ): void {
    const percentage = Math.round((current / total) * 100);
    const filled = Math.round((current / total) * width);
    const empty = width - filled;

    const _bar = '█'.repeat(filled) + '░'.repeat(empty);
    const coloredBar = chalk.green('█'.repeat(filled)) + chalk.gray('░'.repeat(empty));
    
    process.stdout.write('\r' + ' '.repeat(80) + '\r'); // 清除当前行
    process.stdout.write(
      `${chalk.cyan('📊')} ${message} [${coloredBar}] ${chalk.yellow(percentage + '%')} (${current}/${total})`
    );

    if (current >= total) {
      process.stdout.write('\n');
    }
  }

  /**
   * 显示打字机效果
   */
  public static async typeWriter(
    text: string,
    speed: number = 50,
    _color: string = 'white'
  ): Promise<void> {
    const coloredText = chalk.white;
    
    for (let i = 0; i < text.length; i++) {
      process.stdout.write(coloredText(text[i]));
      await this.sleep(speed);
    }
    process.stdout.write('\n');
  }

  /**
   * 显示脉冲动画
   */
  public static showPulse(
    text: string,
    options: AnimationOptions = {}
  ): string {
    const {
      interval = 800,
      color = 'blue'
    } = options;

    const animationId = Math.random().toString(36).substr(2, 9);
    let bright = true;

    const animation = setInterval(() => {
      process.stdout.write('\r' + ' '.repeat(80) + '\r'); // 清除当前行
      const styledText = bright ?
        chalk.bold.blue(text) :
        chalk.dim.blue(text);
      process.stdout.write(styledText);
      bright = !bright;
    }, interval);

    this.activeAnimations.set(animationId, animation);
    return animationId;
  }

  /**
   * 显示波浪动画
   */
  public static showWave(
    message: string = '加载中',
    options: AnimationOptions = {}
  ): string {
    const {
      interval = 300,
      color = 'cyan'
    } = options;

    const waves = ['▁', '▂', '▃', '▄', '▅', '▆', '▇', '█', '▇', '▆', '▅', '▄', '▃', '▂'];
    let waveIndex = 0;
    const animationId = Math.random().toString(36).substr(2, 9);

    const animation = setInterval(() => {
      process.stdout.write('\r' + ' '.repeat(80) + '\r'); // 清除当前行
      const wave = waves.slice(waveIndex, waveIndex + 8).join('');
      const coloredWave = chalk.cyan(wave);
      process.stdout.write(`${coloredWave} ${message}`);
      waveIndex = (waveIndex + 1) % waves.length;
    }, interval);

    this.activeAnimations.set(animationId, animation);
    return animationId;
  }

  /**
   * 显示成功动画
   */
  public static showSuccess(message: string): void {
    const frames = ['✓', '✓', '✓'];
    let frameIndex = 0;

    const animation = setInterval(() => {
      process.stdout.write('\r' + ' '.repeat(80) + '\r'); // 清除当前行
      const frame = frames[frameIndex];
      const text = frameIndex < 2 ? 
        chalk.green.bold(frame) + ' ' + chalk.white(message) :
        chalk.green('✅') + ' ' + chalk.green(message);
      process.stdout.write(text);
      frameIndex++;

      if (frameIndex >= frames.length) {
        clearInterval(animation);
        process.stdout.write('\n');
      }
    }, 200);
  }

  /**
   * 显示错误动画
   */
  public static showError(message: string): void {
    const frames = ['✗', '✗', '✗'];
    let frameIndex = 0;

    const animation = setInterval(() => {
      process.stdout.write('\r' + ' '.repeat(80) + '\r'); // 清除当前行
      const frame = frames[frameIndex];
      const text = frameIndex < 2 ? 
        chalk.red.bold(frame) + ' ' + chalk.white(message) :
        chalk.red('❌') + ' ' + chalk.red(message);
      process.stdout.write(text);
      frameIndex++;

      if (frameIndex >= frames.length) {
        clearInterval(animation);
        process.stdout.write('\n');
      }
    }, 200);
  }

  /**
   * 显示警告动画
   */
  public static showWarning(message: string): void {
    const frames = ['⚠', '⚠', '⚠'];
    let frameIndex = 0;

    const animation = setInterval(() => {
      process.stdout.write('\r' + ' '.repeat(80) + '\r'); // 清除当前行
      const frame = frames[frameIndex];
      const text = frameIndex < 2 ? 
        chalk.yellow.bold(frame) + ' ' + chalk.white(message) :
        chalk.yellow('⚠️') + ' ' + chalk.yellow(message);
      process.stdout.write(text);
      frameIndex++;

      if (frameIndex >= frames.length) {
        clearInterval(animation);
        process.stdout.write('\n');
      }
    }, 200);
  }

  /**
   * 显示淡入效果
   */
  public static async fadeIn(text: string, steps: number = 10): Promise<void> {
    const stepDelay = 50;
    
    for (let i = 0; i <= steps; i++) {
      const opacity = i / steps;
      const alpha = Math.round(opacity * 255);
      
      process.stdout.write('\r' + ' '.repeat(80) + '\r'); // 清除当前行
      
      if (opacity < 0.3) {
        process.stdout.write(chalk.gray(text));
      } else if (opacity < 0.7) {
        process.stdout.write(chalk.white(text));
      } else {
        process.stdout.write(chalk.bold.white(text));
      }
      
      await this.sleep(stepDelay);
    }
    process.stdout.write('\n');
  }

  /**
   * 显示彩虹文字
   */
  public static rainbow(text: string): string {
    const colors = ['red', 'yellow', 'green', 'cyan', 'blue', 'magenta'];
    let result = '';
    
    for (let i = 0; i < text.length; i++) {
      const color = colors[i % colors.length];
      result += chalk.cyan(text[i]);
    }
    
    return result;
  }

  /**
   * 创建边框
   */
  public static createBox(
    content: string[],
    title?: string,
    style: 'single' | 'double' | 'rounded' = 'single'
  ): string {
    const styles = {
      single: { h: '─', v: '│', tl: '┌', tr: '┐', bl: '└', br: '┘' },
      double: { h: '═', v: '║', tl: '╔', tr: '╗', bl: '╚', br: '╝' },
      rounded: { h: '─', v: '│', tl: '╭', tr: '╮', bl: '╰', br: '╯' }
    };

    const chars = styles[style];
    const maxLength = Math.max(...content.map(line => this.stripAnsi(line).length));
    const width = Math.max(maxLength + 4, title ? title.length + 4 : 0);

    let result = '';
    
    // 顶部边框
    if (title) {
      const titlePadding = Math.max(0, width - title.length - 4);
      result += chars.tl + chars.h + ' ' + title + ' ' + chars.h.repeat(titlePadding) + chars.tr + '\n';
    } else {
      result += chars.tl + chars.h.repeat(width - 2) + chars.tr + '\n';
    }

    // 内容
    content.forEach(line => {
      const padding = width - this.stripAnsi(line).length - 4;
      result += chars.v + ' ' + line + ' '.repeat(padding) + ' ' + chars.v + '\n';
    });

    // 底部边框
    result += chars.bl + chars.h.repeat(width - 2) + chars.br;

    return result;
  }

  /**
   * 停止动画
   */
  public static stopAnimation(animationId: string): void {
    const animation = this.activeAnimations.get(animationId);
    if (animation) {
      clearInterval(animation);
      this.activeAnimations.delete(animationId);
      process.stdout.write('\r' + ' '.repeat(80) + '\r'); // 清除当前行
    }
  }

  /**
   * 停止所有动画
   */
  public static stopAllAnimations(): void {
    this.activeAnimations.forEach((animation, id) => {
      clearInterval(animation);
    });
    this.activeAnimations.clear();
    process.stdout.write('\r' + ' '.repeat(80) + '\r'); // 清除当前行
  }

  /**
   * 设置主题
   */
  public static setTheme(themeName: string): void {
    if (this.themes[themeName]) {
      this.currentTheme = themeName;
    }
  }

  /**
   * 获取主题颜色
   */
  public static getThemeColor(colorName: keyof ThemeColors): string {
    return this.themes[this.currentTheme][colorName];
  }

  /**
   * 应用主题颜色
   */
  public static themed(text: string, colorName: keyof ThemeColors): string {
    const color = this.getThemeColor(colorName);
    return chalk.hex(color)(text);
  }

  /**
   * 清屏并显示启动动画
   */
  public static async showStartupAnimation(): Promise<void> {
    console.clear();
    
    // 显示Logo
    const logo = [
      '  ██████╗██╗  ██╗ █████╗ ████████╗███████╗██████╗ ',
      ' ██╔════╝██║  ██║██╔══██╗╚══██╔══╝██╔════╝██╔══██╗',
      ' ██║     ███████║███████║   ██║   █████╗  ██████╔╝',
      ' ██║     ██╔══██║██╔══██║   ██║   ██╔══╝  ██╔══██╗',
      ' ╚██████╗██║  ██║██║  ██║   ██║   ███████╗██║  ██║',
      '  ╚═════╝╚═╝  ╚═╝╚═╝  ╚═╝   ╚═╝   ╚══════╝╚═╝  ╚═╝'
    ];

    for (const line of logo) {
      console.log(chalk.cyan(line));
      await this.sleep(100);
    }

    await this.sleep(500);
    
    // 显示版本信息
    await this.typeWriter('🤖 AI 开发助手 v0.3.3', 30, 'gray');
    await this.sleep(300);
    await this.typeWriter('✨ 正在启动...', 50, 'cyan');
    
    await this.sleep(1000);
  }

  /**
   * 睡眠函数
   */
  private static sleep(ms: number): Promise<void> {
    return new Promise(resolve => setTimeout(resolve, ms));
  }

  /**
   * 移除ANSI颜色代码
   */
  private static stripAnsi(str: string): string {
    // eslint-disable-next-line no-control-regex
    return str.replace(/\x1b\[[0-9;]*m/g, '');
  }
}
