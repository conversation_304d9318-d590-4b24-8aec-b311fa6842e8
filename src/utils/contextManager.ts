import { ChatMessage } from '../types';

/**
 * 上下文管理器 - 处理超长上下文的分割和优化
 */
export class ContextManager {
  // 不同模型的token限制
  private static readonly TOKEN_LIMITS: Record<string, number> = {
    'deepseek-chat': 65536,
    'deepseek-reasoner': 65536,
    'deepseek-coder': 65536,
    'gpt-4': 8192,
    'gpt-3.5-turbo': 4096
  };

  // 估算文本token数量（粗略估算：1 token ≈ 0.75 个英文单词 ≈ 1个中文字符）
  public static estimateTokens(text: string): number {
    // 中文字符
    const chineseChars = (text.match(/[\u4e00-\u9fff]/g) || []).length;
    // 英文单词
    const englishWords = (text.match(/[a-zA-Z]+/g) || []).length;
    // 其他字符（符号、空格等）
    const otherChars = text.length - chineseChars - englishWords;
    
    return Math.ceil(chineseChars * 1.2 + englishWords * 1.3 + otherChars * 0.3);
  }

  /**
   * 检查文本是否超过模型token限制
   */
  public static checkTokenLimit(text: string, model: string, reserveTokens: number = 2000): {
    isValid: boolean;
    currentTokens: number;
    maxTokens: number;
    exceedsBy: number;
  } {
    const currentTokens = this.estimateTokens(text);
    const maxTokens = this.TOKEN_LIMITS[model] || 4096;
    const availableTokens = maxTokens - reserveTokens;
    const exceedsBy = currentTokens - availableTokens;
    
    return {
      isValid: exceedsBy <= 0,
      currentTokens,
      maxTokens: availableTokens,
      exceedsBy: Math.max(0, exceedsBy)
    };
  }

  /**
   * 智能截断文本以适应token限制
   */
  public static truncateText(text: string, model: string, reserveTokens: number = 2000): {
    truncatedText: string;
    wasTruncated: boolean;
    removedTokens: number;
  } {
    const check = this.checkTokenLimit(text, model, reserveTokens);
    
    if (check.isValid) {
      return {
        truncatedText: text,
        wasTruncated: false,
        removedTokens: 0
      };
    }

    // 计算需要保留的文本比例
    const keepRatio = check.maxTokens / check.currentTokens;
    const targetLength = Math.floor(text.length * keepRatio * 0.9); // 留10%安全边距
    
    // 智能截断：优先保留开头和结尾
    const startKeep = Math.floor(targetLength * 0.7);
    const endKeep = targetLength - startKeep;
    
    const truncatedText = text.substring(0, startKeep) + 
      '\n\n... [内容过长，已省略中间部分] ...\n\n' +
      text.substring(text.length - endKeep);

    return {
      truncatedText,
      wasTruncated: true,
      removedTokens: check.exceedsBy
    };
  }

  /**
   * 分割长文本为多个块
   */
  public static splitText(text: string, model: string, reserveTokens: number = 2000): {
    chunks: string[];
    totalChunks: number;
  } {
    const check = this.checkTokenLimit(text, model, reserveTokens);
    
    if (check.isValid) {
      return {
        chunks: [text],
        totalChunks: 1
      };
    }

    // 计算需要分割的块数
    const chunksNeeded = Math.ceil(check.currentTokens / check.maxTokens);
    const chunkSize = Math.floor(text.length / chunksNeeded);
    
    const chunks: string[] = [];
    
    for (let i = 0; i < chunksNeeded; i++) {
      const start = i * chunkSize;
      const end = Math.min((i + 1) * chunkSize, text.length);
      
      // 在句号、换行符或段落边界处分割
      let chunk = text.substring(start, end);
      if (i < chunksNeeded - 1) {
        const lastSentence = chunk.lastIndexOf('。');
        const lastNewline = chunk.lastIndexOf('\n');
        const breakPoint = Math.max(lastSentence, lastNewline);
        
        if (breakPoint > chunk.length * 0.7) {
          chunk = chunk.substring(0, breakPoint + 1);
        }
      }
      
      chunks.push(chunk);
    }

    return {
      chunks,
      totalChunks: chunks.length
    };
  }

  /**
   * 优化提示词长度
   */
  public static optimizePrompt(prompt: string, model: string): {
    optimizedPrompt: string;
    warnings: string[];
  } {
    const warnings: string[] = [];
    let optimizedPrompt = prompt;

    // 检查长度
    const check = this.checkTokenLimit(prompt, model);
    
    if (!check.isValid) {
      warnings.push(`提示词过长，当前 ${check.currentTokens} tokens，超出 ${check.exceedsBy} tokens`);
      
      // 尝试截断
      const truncated = this.truncateText(prompt, model);
      optimizedPrompt = truncated.truncatedText;
      
      if (truncated.wasTruncated) {
        warnings.push(`已自动截断提示词，移除了约 ${truncated.removedTokens} tokens`);
      }
    }

    return {
      optimizedPrompt,
      warnings
    };
  }

  /**
   * 构建渐进式上下文（对于超长任务，分阶段提供上下文）
   */
  public static buildProgressiveContext(
    baseContext: string,
    additionalInfo: string[],
    model: string
  ): {
    context: string;
    skippedInfo: string[];
    warnings: string[];
  } {
    const warnings: string[] = [];
    const skippedInfo: string[] = [];
    let context = baseContext;

    for (const info of additionalInfo) {
      const testContext = context + '\n\n' + info;
      const check = this.checkTokenLimit(testContext, model);
      
      if (check.isValid) {
        context = testContext;
      } else {
        skippedInfo.push(info);
        warnings.push(`跳过了部分上下文信息以避免超出token限制`);
      }
    }

    return {
      context,
      skippedInfo,
      warnings
    };
  }
}