export interface ChatMessage {
  role: 'user' | 'assistant' | 'system' | 'tool';
  content: string;
  timestamp: Date;
  toolCalls?: ToolCall[];
  toolCallId?: string;
}

export interface ToolCall {
  id: string;
  name: string;
  arguments: Record<string, any>;
}

export interface Tool {
  name: string;
  description: string;
  parameters: {
    type: 'object';
    properties: Record<string, {
      type: string;
      description: string;
      enum?: string[];
    }>;
    required: string[];
  };
  execute: (args: Record<string, any>) => Promise<any>;
}

export interface ChatSession {
  id: string;
  title: string;
  messages: ChatMessage[];
  createdAt: Date;
  updatedAt: Date;
}

export interface ChaterConfig {
  defaultModel: string;
  apiKeys: {
    openai?: string;
    anthropic?: string;
    google?: string;
    deepseek?: string;
  };
  models: {
    [key: string]: {
      provider: 'openai' | 'anthropic' | 'google' | 'deepseek';
      model: string;
      temperature?: number;
      maxTokens?: number;
    };
  };
  storage: {
    historyLimit: number;
    sessionLimit: number;
  };
  mcp?: {
    servers: MCPServerConfig[];
    settings: MCPManagerConfig;
  };
}

// 导入 MCP 相关类型
import { MCPServerConfig, MCPManagerConfig } from './mcp';

export interface ModelProvider {
  name: string;
  chat(messages: ChatMessage[], tools?: Tool[]): Promise<{
    content: string;
    toolCalls?: ToolCall[];
  }>;
  isAvailable(): boolean;
  supportsTools(): boolean;
}