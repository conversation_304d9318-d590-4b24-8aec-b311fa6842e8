import inquirer from 'inquirer';
import chalk from 'chalk';
import { ContextManager } from '../utils/contextManager';
import { Task, TaskStatus, TaskPriority, CreateTaskRequest, TaskFilter } from '../types/task';
import { TaskManager } from './taskManager';

export class TaskInteraction {
  constructor(private taskManager: TaskManager) {}

  /**
   * 显示任务创建界面
   */
  public async showCreateTaskInterface(): Promise<Task | null> {
    console.log(chalk.blue('\n📋 创建新任务'));
    
    try {
      const answers = await inquirer.prompt([
        {
          type: 'input',
          name: 'description',
          message: '请描述你要完成的任务:',
          validate: (input) => {
            if (input.trim().length === 0) {
              return '任务描述不能为空';
            }
            
            // 检查描述长度
            const tokenCheck = ContextManager.checkTokenLimit(input, 'deepseek-reasoner', 5000);
            if (!tokenCheck.isValid) {
              return `任务描述过长(约${tokenCheck.currentTokens} tokens)，建议简化描述或分解为更小的任务`;
            }
            
            if (tokenCheck.currentTokens > 30000) {
              return '任务描述过于复杂，建议分解为多个简单任务';
            }
            
            return true;
          }
        },
        {
          type: 'list',
          name: 'priority',
          message: '选择任务优先级:',
          choices: [
            { name: '🔥 紧急', value: 'urgent' },
            { name: '⚡ 高', value: 'high' },
            { name: '📋 中', value: 'medium' },
            { name: '📝 低', value: 'low' }
          ],
          default: 'medium'
        },
        {
          type: 'confirm',
          name: 'confirm',
          message: '确认创建任务?',
          default: true
        }
      ]);

      if (!answers.confirm) {
        console.log(chalk.yellow('❌ 任务创建已取消'));
        return null;
      }

      // 显示任务描述的token估算
      const tokenCount = ContextManager.estimateTokens(answers.description);
      if (tokenCount > 10000) {
        console.log(chalk.yellow(`⚠️  任务描述较长 (约${tokenCount} tokens)，将使用分段处理`));
      }

      const request: CreateTaskRequest = {
        description: answers.description,
        priority: answers.priority as TaskPriority,
        context: {
          projectPath: process.cwd(),
          relevantFiles: [], // 可以扩展为扫描项目文件
          environmentInfo: {},
          userPreferences: {}
        }
      };

      return await this.taskManager.createTask(request);

    } catch (error) {
      console.log(chalk.red('❌ 任务创建失败'));
      return null;
    }
  }

  /**
   * 显示任务列表
   */
  public async showTaskList(): Promise<void> {
    const tasks = this.taskManager.getAllTasks();
    
    if (tasks.length === 0) {
      console.log(chalk.yellow('📝 暂无任务'));
      return;
    }

    console.log(chalk.blue('\n📋 任务列表'));
    
    // 按状态分组显示
    const tasksByStatus = tasks.reduce((acc, task) => {
      if (!acc[task.status]) acc[task.status] = [];
      acc[task.status].push(task);
      return acc;
    }, {} as Record<TaskStatus, Task[]>);

    Object.entries(tasksByStatus).forEach(([status, statusTasks]) => {
      const statusIcon = this.getStatusIcon(status as TaskStatus);
      const statusColor = this.getStatusColor(status as TaskStatus);
      
      console.log(statusColor(`\n${statusIcon} ${this.getStatusLabel(status as TaskStatus)} (${statusTasks.length})`));
      
      statusTasks.forEach(task => {
        const priorityIcon = this.getPriorityIcon(task.priority);
        const complexityIcon = this.getComplexityIcon(task.complexity);
        const timeInfo = this.getTimeInfo(task);
        
        console.log(`  ${priorityIcon}${complexityIcon} ${task.title}`);
        console.log(chalk.gray(`    ID: ${task.id.slice(0, 8)}... | ${timeInfo}`));
        
        if (task.executionPlan && task.totalSteps > 0) {
          const progress = `${task.currentStep}/${task.totalSteps}`;
          console.log(chalk.gray(`    进度: ${progress} 步骤`));
        }
      });
    });
  }

  /**
   * 显示任务详情
   */
  public async showTaskDetails(taskId: string): Promise<void> {
    const task = this.taskManager.getTask(taskId);
    if (!task) {
      console.log(chalk.red('❌ 任务不存在'));
      return;
    }

    console.log(chalk.blue('\n📋 任务详情'));
    console.log(`${this.getStatusIcon(task.status)} ${task.title}`);
    console.log(chalk.gray(`ID: ${task.id}`));
    console.log(chalk.gray(`状态: ${this.getStatusLabel(task.status)}`));
    console.log(chalk.gray(`优先级: ${this.getPriorityLabel(task.priority)}`));
    console.log(chalk.gray(`复杂度: ${task.complexity}`));
    console.log(chalk.gray(`创建时间: ${task.createdAt.toLocaleString('zh-CN')}`));
    
    if (task.startedAt) {
      console.log(chalk.gray(`开始时间: ${task.startedAt.toLocaleString('zh-CN')}`));
    }
    
    if (task.completedAt) {
      console.log(chalk.gray(`完成时间: ${task.completedAt.toLocaleString('zh-CN')}`));
    }

    console.log(chalk.cyan('\n📝 任务描述:'));
    console.log(`  ${task.description}`);

    if (task.executionPlan) {
      console.log(chalk.cyan('\n📋 执行计划:'));
      console.log(`  ${task.executionPlan.title}`);
      console.log(`  ${task.executionPlan.description}`);
      console.log(chalk.gray(`  预估时间: ${task.executionPlan.estimatedTime} 分钟`));
      
      if (task.executionPlan.dependencies.length > 0) {
        console.log(chalk.gray(`  依赖: ${task.executionPlan.dependencies.join(', ')}`));
      }

      console.log(chalk.cyan('\n📋 执行步骤:'));
      task.executionPlan.steps.forEach((step, index) => {
        const stepStatus = this.getStatusIcon(step.status);
        const isCurrent = index === task.currentStep;
        const stepColor = isCurrent ? chalk.yellow : chalk.gray;
        
        console.log(stepColor(`  ${stepStatus} ${index + 1}. ${step.title}`));
        if (isCurrent) {
          console.log(stepColor(`     ${step.description}`));
        }
      });
    }

    if (task.executionLog.length > 0) {
      console.log(chalk.cyan('\n📜 执行日志 (最近5条):'));
      task.executionLog.slice(-5).forEach(log => {
        const logIcon = this.getLogIcon(log.type);
        const timestamp = log.timestamp.toLocaleTimeString('zh-CN');
        console.log(`  ${logIcon} ${timestamp} ${log.message}`);
      });
    }

    if (task.userNotes.length > 0) {
      console.log(chalk.cyan('\n📝 用户备注:'));
      task.userNotes.forEach(note => {
        console.log(`  💬 ${note}`);
      });
    }
  }

  /**
   * 显示任务管理菜单
   */
  public async showTaskMenu(): Promise<string | null> {
    const tasks = this.taskManager.getAllTasks();
    
    if (tasks.length === 0) {
      console.log(chalk.yellow('📝 暂无任务，请先创建任务'));
      return null;
    }

    const choices = tasks.map(task => ({
      name: `${this.getStatusIcon(task.status)} ${task.title} (${this.getStatusLabel(task.status)})`,
      value: task.id,
      short: task.title
    }));

    choices.unshift(
      { name: '📊 查看统计信息', value: 'stats', short: '统计' },
      { name: '🔍 搜索任务', value: 'search', short: '搜索' },
      { name: '↩️  返回', value: 'back', short: '返回' }
    );

    try {
      const { selectedTask } = await inquirer.prompt([
        {
          type: 'list',
          name: 'selectedTask',
          message: '选择要操作的任务:',
          choices,
          pageSize: 10
        }
      ]);

      return selectedTask;
    } catch (error) {
      return null;
    }
  }

  /**
   * 显示任务操作菜单
   */
  public async showTaskActions(taskId: string): Promise<string | null> {
    const task = this.taskManager.getTask(taskId);
    if (!task) return null;

    const actions = [];

    // 根据任务状态显示不同的操作
    switch (task.status) {
      case 'ready':
        actions.push({ name: '▶️  执行任务', value: 'execute' });
        break;
      case 'in_progress':
        actions.push({ name: '⏸️  暂停任务', value: 'pause' });
        break;
      case 'paused':
        actions.push({ name: '▶️  恢复任务', value: 'resume' });
        break;
    }

    if (task.status !== 'completed' && task.status !== 'cancelled') {
      actions.push({ name: '❌ 取消任务', value: 'cancel' });
    }

    actions.push(
      { name: '📋 查看详情', value: 'details' },
      { name: '📝 添加备注', value: 'note' },
      { name: '📜 查看日志', value: 'logs' },
      { name: '↩️  返回', value: 'back' }
    );

    try {
      const { action } = await inquirer.prompt([
        {
          type: 'list',
          name: 'action',
          message: `任务操作 - ${task.title}:`,
          choices: actions
        }
      ]);

      return action;
    } catch (error) {
      return null;
    }
  }

  /**
   * 显示任务统计
   */
  public showTaskStatistics(): void {
    const stats = this.taskManager.getTaskStatistics();

    console.log(chalk.blue('\n📊 任务统计'));
    console.log(`📝 总任务数: ${stats.total}`);
    console.log(`✅ 完成率: ${stats.completionRate}%`);

    console.log(chalk.cyan('\n📋 按状态分布:'));
    Object.entries(stats.byStatus).forEach(([status, count]) => {
      const icon = this.getStatusIcon(status as TaskStatus);
      const label = this.getStatusLabel(status as TaskStatus);
      console.log(`  ${icon} ${label}: ${count}`);
    });

    console.log(chalk.cyan('\n🎯 按复杂度分布:'));
    Object.entries(stats.byComplexity).forEach(([complexity, count]) => {
      const icon = this.getComplexityIcon(complexity);
      console.log(`  ${icon} ${complexity}: ${count}`);
    });

    console.log(chalk.cyan('\n⚡ 按优先级分布:'));
    Object.entries(stats.byPriority).forEach(([priority, count]) => {
      const icon = this.getPriorityIcon(priority as TaskPriority);
      const label = this.getPriorityLabel(priority as TaskPriority);
      console.log(`  ${icon} ${label}: ${count}`);
    });
  }

  /**
   * 搜索任务界面
   */
  public async showTaskSearch(): Promise<void> {
    try {
      const { searchText } = await inquirer.prompt([
        {
          type: 'input',
          name: 'searchText',
          message: '输入搜索关键词:',
          validate: (input) => input.trim().length > 0 || '请输入搜索关键词'
        }
      ]);

      const filter: TaskFilter = { searchText };
      const results = this.taskManager.filterTasks(filter);

      if (results.length === 0) {
        console.log(chalk.yellow('🔍 未找到匹配的任务'));
        return;
      }

      console.log(chalk.green(`\n🔍 找到 ${results.length} 个匹配的任务:`));
      
      results.forEach(task => {
        const statusIcon = this.getStatusIcon(task.status);
        const priorityIcon = this.getPriorityIcon(task.priority);
        console.log(`  ${statusIcon}${priorityIcon} ${task.title}`);
        console.log(chalk.gray(`    ${task.description.slice(0, 100)}...`));
      });
    } catch (error) {
      console.log(chalk.red('❌ 搜索失败'));
    }
  }

  /**
   * 添加任务备注
   */
  public async addTaskNote(taskId: string): Promise<void> {
    try {
      const { note } = await inquirer.prompt([
        {
          type: 'input',
          name: 'note',
          message: '添加备注:',
          validate: (input) => input.trim().length > 0 || '备注不能为空'
        }
      ]);

      this.taskManager.updateTask({
        taskId,
        userNotes: [note]
      });

      console.log(chalk.green('✅ 备注已添加'));
    } catch (error) {
      console.log(chalk.red('❌ 添加备注失败'));
    }
  }

  // 辅助方法
  private getStatusIcon(status: TaskStatus): string {
    const icons = {
      'pending': '⏳',
      'planning': '🧠',
      'ready': '🎯',
      'in_progress': '🔄',
      'paused': '⏸️',
      'waiting_input': '❓',
      'completed': '✅',
      'failed': '❌',
      'cancelled': '🚫'
    };
    return icons[status] || '❓';
  }

  private getStatusColor(status: TaskStatus): (text: string) => string {
    const colors = {
      'pending': chalk.gray,
      'planning': chalk.blue,
      'ready': chalk.cyan,
      'in_progress': chalk.yellow,
      'paused': chalk.magenta,
      'waiting_input': chalk.yellow,
      'completed': chalk.green,
      'failed': chalk.red,
      'cancelled': chalk.red
    };
    return colors[status] || chalk.gray;
  }

  private getStatusLabel(status: TaskStatus): string {
    const labels = {
      'pending': '等待开始',
      'planning': '规划中',
      'ready': '准备执行',
      'in_progress': '执行中',
      'paused': '已暂停',
      'waiting_input': '等待输入',
      'completed': '已完成',
      'failed': '执行失败',
      'cancelled': '已取消'
    };
    return labels[status] || status;
  }

  private getPriorityIcon(priority: TaskPriority): string {
    const icons = {
      'urgent': '🔥',
      'high': '⚡',
      'medium': '📋',
      'low': '📝'
    };
    return icons[priority] || '📋';
  }

  private getPriorityLabel(priority: TaskPriority): string {
    const labels = {
      'urgent': '紧急',
      'high': '高',
      'medium': '中',
      'low': '低'
    };
    return labels[priority] || priority;
  }

  private getComplexityIcon(complexity: string): string {
    const icons: Record<string, string> = {
      'simple': '🟢',
      'moderate': '🟡',
      'complex': '🟠',
      'very_complex': '🔴'
    };
    return icons[complexity] || '⚪';
  }

  private getLogIcon(type: string): string {
    const icons: Record<string, string> = {
      'info': 'ℹ️',
      'success': '✅',
      'warning': '⚠️',
      'error': '❌',
      'user_input': '💬',
      'ai_reasoning': '🧠',
      'tool_call': '🔧',
      'validation': '🔍'
    };
    return icons[type] || 'ℹ️';
  }

  private getTimeInfo(task: Task): string {
    if (task.completedAt && task.startedAt) {
      const duration = Math.round((task.completedAt.getTime() - task.startedAt.getTime()) / 60000);
      return `用时 ${duration} 分钟`;
    } else if (task.startedAt) {
      const elapsed = Math.round((Date.now() - task.startedAt.getTime()) / 60000);
      return `已执行 ${elapsed} 分钟`;
    } else {
      return `预估 ${task.estimatedDuration} 分钟`;
    }
  }
}