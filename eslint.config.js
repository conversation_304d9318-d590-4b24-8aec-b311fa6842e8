const js = require('@eslint/js');
const tseslint = require('typescript-eslint');

module.exports = tseslint.config(
  {
    // 忽略文件配置
    ignores: [
      'dist/**',
      'node_modules/**',
      'test-commit.js'
    ]
  },
  
  // JavaScript 推荐规则
  js.configs.recommended,
  
  // TypeScript 推荐规则
  ...tseslint.configs.recommended,
  
  {
    // TypeScript 文件配置
    files: ['**/*.ts', '**/*.tsx'],
    languageOptions: {
      parser: tseslint.parser,
      parserOptions: {
        project: './tsconfig.json',
        tsconfigRootDir: __dirname,
        ecmaVersion: 2020,
        sourceType: 'module'
      }
    },
    plugins: {
      '@typescript-eslint': tseslint.plugin
    },
    rules: {
      // 基础规则
      'no-console': 'off', // CLI 工具需要 console
      'no-unused-vars': 'off', // 使用 TypeScript 版本
      
      // TypeScript 特定规则
      '@typescript-eslint/no-unused-vars': ['warn', { 
        argsIgnorePattern: '^_',
        varsIgnorePattern: '^_',
        ignoreRestSiblings: true
      }],
      '@typescript-eslint/explicit-function-return-type': 'off',
      '@typescript-eslint/explicit-module-boundary-types': 'off',
      '@typescript-eslint/no-explicit-any': 'off', // 暂时关闭，避免大量警告
      '@typescript-eslint/no-non-null-assertion': 'warn',
      '@typescript-eslint/no-var-requires': 'error',
      
      // 代码风格
      'prefer-const': 'warn',
      'no-var': 'error',
      'object-shorthand': 'warn',
      'prefer-arrow-callback': 'off',
      
      // 潜在错误
      'no-duplicate-imports': 'error',
      'no-template-curly-in-string': 'error',
      'no-unreachable-loop': 'error',
      'no-useless-escape': 'warn',
      'no-case-declarations': 'off',
      
      // 最佳实践
      'eqeqeq': ['error', 'always'],
      'curly': 'off', // 关闭强制大括号规则
      'no-eval': 'error',
      'no-implied-eval': 'error',
      'no-new-func': 'error',
      'no-return-assign': 'error'
    }
  },
  
  {
    // 配置文件特殊规则
    files: ['eslint.config.js'],
    languageOptions: {
      sourceType: 'script',
      ecmaVersion: 2022
    },
    rules: {
      '@typescript-eslint/no-var-requires': 'off'
    }
  }
);