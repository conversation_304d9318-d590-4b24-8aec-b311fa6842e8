import inquirer from 'inquirer';
import chalk from 'chalk';
import ora from 'ora';
import { exec } from 'child_process';
import { promisify } from 'util';
import { ConfigManager } from './config';
import { ChatManager } from './chat';
import { MemoryManager } from './memory';
import { FileScanner } from '../utils/fileScanner';
import { ShortcutParser, ShortcutResult } from '../utils/shortcutParser';
import { SmartInquirer } from '../utils/smartInquirer';
import { TaskManager } from './taskManager';
import { TaskInteraction } from './taskInteraction';

const execAsync = promisify(exec);

export interface Command {
  name: string;
  description: string;
  aliases?: string[];
  handler: (args: string[], context: InteractiveContext) => Promise<void>;
}

export interface InteractiveContext {
  config: ConfigManager;
  chat: ChatManager;
  memory: MemoryManager;
  scanner: FileScanner;
  projectPath: string;
  isProjectInitialized: boolean;
}

export class InteractiveCLI {
  private context: InteractiveContext;
  private commands: Map<string, Command> = new Map();
  private running = false;
  private shortcutParser: ShortcutParser;
  private smartInquirer: SmartInquirer;
  private taskManager: TaskManager;
  private taskInteraction: TaskInteraction;
  private useModernUI: boolean = false;

  constructor() {
    const config = new ConfigManager();
    this.context = {
      config,
      chat: new ChatManager(config),
      memory: new MemoryManager(),
      scanner: new FileScanner(),
      projectPath: process.cwd(),
      isProjectInitialized: false
    };

    this.shortcutParser = new ShortcutParser(this.context.projectPath);
    this.smartInquirer = new SmartInquirer(this.context.projectPath);
    this.taskManager = new TaskManager(this.context.config);
    this.taskInteraction = new TaskInteraction(this.taskManager);
    this.registerCommands();
  }

  private registerCommands(): void {
    const commands: Command[] = [
      {
        name: 'help',
        description: '显示帮助信息',
        aliases: ['h', '?'],
        handler: this.handleHelp.bind(this)
      },
      {
        name: 'init',
        description: '初始化项目记忆',
        handler: this.handleInit.bind(this)
      },
      {
        name: 'config',
        description: '配置 API 密钥',
        handler: this.handleConfig.bind(this)
      },
      {
        name: 'models',
        description: '显示可用模型',
        handler: this.handleModels.bind(this)
      },
      {
        name: 'memory',
        description: '查看项目记忆',
        aliases: ['mem'],
        handler: this.handleMemory.bind(this)
      },
      {
        name: 'insight',
        description: '添加重要洞察',
        handler: this.handleInsight.bind(this)
      },
      {
        name: 'todo',
        description: '添加待办事项',
        handler: this.handleTodo.bind(this)
      },
      {
        name: 'clear',
        description: '清除屏幕',
        aliases: ['cls'],
        handler: this.handleClear.bind(this)
      },
      {
        name: 'exit',
        description: '退出 chater',
        aliases: ['quit', 'q'],
        handler: this.handleExit.bind(this)
      },
      {
        name: 'pwd',
        description: '显示当前目录',
        handler: this.handlePwd.bind(this)
      },
      {
        name: 'ls',
        description: '列出文件',
        handler: this.handleLs.bind(this)
      },
      {
        name: 'shortcuts',
        description: '显示快捷键帮助',
        aliases: ['keys', 'hotkeys'],
        handler: this.handleShortcuts.bind(this)
      },
      {
        name: 'history',
        description: '显示命令历史',
        aliases: ['hist'],
        handler: this.handleHistory.bind(this)
      },
      {
        name: 'stats',
        description: '显示使用统计',
        handler: this.handleStats.bind(this)
      },
      {
        name: 'task',
        description: '任务管理系统',
        aliases: ['t', 'tasks'],
        handler: this.handleTask.bind(this)
      }
    ];

    commands.forEach(cmd => {
      this.commands.set(cmd.name, cmd);
      if (cmd.aliases) {
        cmd.aliases.forEach(alias => this.commands.set(alias, cmd));
      }
    });
  }

  public async start(useModernUI: boolean = false): Promise<void> {
    this.running = true;
    this.useModernUI = useModernUI;

    // 初始化检查
    await this.initialize();

    // 如果启用现代化界面
    if (this.useModernUI) {
      await this.startModernInterface();
      return;
    }

    // 显示欢迎信息
    this.showWelcome();

    // 主交互循环
    await this.runInteractiveLoop();

    // 程序退出时清理
    this.cleanup();
  }

  private async initialize(): Promise<void> {
    // 检查项目是否已初始化
    this.context.isProjectInitialized = await this.context.memory.hasMemory();
    
    // 检查 API 密钥
    const availableModels = this.context.chat.getAvailableModels();
    if (availableModels.length === 0) {
      console.log(chalk.yellow('⚠️  检测到未配置 API 密钥'));
      console.log(chalk.gray('提示: 使用 "config" 命令配置 API 密钥'));
    }
  }

  private showWelcome(): void {
    console.log(chalk.blue.bold('🤖 Chater AI 开发助手 v0.3.3'));
    console.log(chalk.gray(`📂 项目: ${this.context.projectPath}`));
    
    if (this.context.isProjectInitialized) {
      console.log(chalk.green('🧠 项目记忆: 已初始化'));
    } else {
      console.log(chalk.yellow('🧠 项目记忆: 未初始化 (使用 "init" 命令初始化)'));
    }
    
    const availableModels = this.context.chat.getAvailableModels();
    if (availableModels.length > 0) {
      console.log(chalk.green(`🤖 可用模型: ${availableModels.join(', ')}`));
    } else {
      console.log(chalk.red('🤖 可用模型: 无 (请先配置 API 密钥)'));
    }
    
    console.log(chalk.gray('💡 输入 "?" 查看快速操作，"??" 选择建议，"help" 查看完整帮助\n'));
  }

  private getPrompt(): string {
    const projectName = this.context.projectPath.split('/').pop() || 'chater';
    const memoryIcon = this.context.isProjectInitialized ? '🧠' : '💭';
    return chalk.cyan(`${memoryIcon} ${projectName}> `);
  }

  private async processInput(input: string): Promise<void> {
    // 使用快捷键解析器解析输入
    const result = this.shortcutParser.parseInput(input);

    await this.handleShortcutResult(result);
  }

  /**
   * 直接处理输入（供现代化界面调用）
   */
  public async processInputDirect(input: string, context: InteractiveContext): Promise<void> {
    // 临时设置上下文
    const originalContext = this.context;
    this.context = context;

    try {
      await this.processInput(input);
    } finally {
      // 恢复原始上下文
      this.context = originalContext;
    }
  }

  private async handleShortcutResult(result: ShortcutResult): Promise<void> {
    switch (result.type) {
      case 'command':
        await this.handleInternalCommand(result);
        break;
      case 'bash':
        await this.handleSystemCommand(result.content);
        break;
      case 'filepath':
        await this.handleFilePath(result);
        break;
      case 'memory':
        await this.handleMemoryShortcut(result);
        break;
      case 'chat':
      default:
        await this.handleChat(result.content);
        break;
    }
  }

  private async handleInternalCommand(result: ShortcutResult): Promise<void> {
    const parts = result.content.split(' ');
    const commandName = parts[0].toLowerCase();
    const args = parts.slice(1);

    // 检查是否是内置命令
    if (this.commands.has(commandName)) {
      const command = this.commands.get(commandName)!;
      await command.handler(args, this.context);
      return;
    }

    // 检查是否是系统命令 (git, npm 等)
    if (this.isSystemCommand(commandName)) {
      await this.handleSystemCommand(result.content);
      return;
    }

    // 未知命令
    console.log(chalk.red(`❌ 未知命令: ${commandName}`));
    console.log(chalk.gray('输入 "help" 查看可用命令，或 "shortcuts" 查看快捷键'));
  }

  private isSystemCommand(command: string): boolean {
    const systemCommands = [
      'git', 'npm', 'yarn', 'pnpm', 'node', 'python', 'pip',
      'docker', 'kubectl', 'curl', 'wget', 'cat', 'head', 'tail',
      'grep', 'find', 'tree', 'code', 'vim', 'nano'
    ];
    return systemCommands.includes(command);
  }

  private async handleSystemCommand(command: string): Promise<void> {
    const spinner = ora(`执行: ${command}`).start();
    
    try {
      const { stdout, stderr } = await execAsync(command, { 
        cwd: this.context.projectPath,
        maxBuffer: 1024 * 1024 // 1MB buffer
      });
      
      spinner.stop();
      
      if (stdout) {
        console.log(stdout);
      }
      
      if (stderr) {
        console.log(chalk.yellow(stderr));
      }
    } catch (error) {
      spinner.stop();
      const err = error as any;
      console.log(chalk.red(`❌ 命令执行失败: ${err.message}`));
      if (err.stdout) console.log(err.stdout);
      if (err.stderr) console.log(chalk.yellow(err.stderr));
    }
  }

  private async handleChat(input: string): Promise<void> {
    const availableModels = this.context.chat.getAvailableModels();
    if (availableModels.length === 0) {
      console.log(chalk.red('❌ 无可用模型，请先使用 "config" 命令配置 API 密钥'));
      return;
    }

    const spinner = ora({
      text: '🧠 Chater 正在思考... (Ctrl+C 强制退出)',
      color: 'cyan'
    }).start();
    
    try {
      const result = await this.context.chat.sendMessage(input);
      
      spinner.stop();
      
      // 显示使用的模型
      const modelDisplay = result.selectedModel === 'deepseek-reasoner' 
        ? chalk.blue('🔬 deepseek-reasoner')
        : chalk.green('💬 deepseek-chat');
      console.log(chalk.gray(`[智能选择: ${modelDisplay}]`));
      
      console.log(chalk.magenta('\n🤖 AI: ') + result.response + '\n');
    } catch (error) {
      spinner.stop();
      
      console.log(chalk.red(`\n❌ 错误: ${error instanceof Error ? error.message : '未知错误'}`));
    }
  }


  // 命令处理器
  private async handleHelp(): Promise<void> {
    console.log(chalk.blue('\n📖 Chater AI 开发助手帮助\n'));
    
    console.log(chalk.yellow('🤖 AI 对话:'));
    console.log('  直接输入问题即可与 AI 对话');
    console.log('  例如: "帮我分析这个项目的架构"\n');
    
    console.log(chalk.yellow('🚀 快捷键:'));
    console.log('  /help         - 显示命令 (/ 为命令快捷键)');
    console.log('  /exit         - 优雅退出 chater');
    console.log('  !git status   - 执行 bash (! 为命令快捷键)');
    console.log('  @package.json - 查看文件 (@ 为文件快捷键)');
    console.log('  #重要发现     - 记录洞察 (# 为记忆快捷键)');
    console.log('  shortcuts     - 查看详细快捷键帮助\n');
    
    console.log(chalk.yellow('🔧 内置命令:'));
    this.commands.forEach((cmd, name) => {
      if (name === cmd.name) { // 只显示主命令，不显示别名
        console.log(`  ${cmd.name.padEnd(12)} - ${cmd.description}`);
      }
    });
    
    console.log(chalk.yellow('\n⚡ 系统命令:'));
    console.log('  git, npm, yarn    - 版本控制和包管理');
    console.log('  ls, cat, grep     - 文件操作');
    console.log('  docker, kubectl   - 容器管理');
    console.log('  code, vim         - 编辑器');
    
    console.log(chalk.red('\n⚠️  退出说明:'));
    console.log('  /exit 或 /quit    - 优雅退出 (推荐)');
    console.log('  Ctrl+C            - 强制退出 (会中断所有操作)\n');
  }

  private async handleInit(): Promise<void> {
    if (this.context.isProjectInitialized) {
      const { overwrite } = await inquirer.prompt([
        {
          type: 'confirm',
          name: 'overwrite',
          message: '检测到已存在的项目记忆，是否重新初始化？',
          default: false
        }
      ]);
      
      if (!overwrite) {
        console.log(chalk.yellow('📄 使用现有的项目记忆'));
        return;
      }
    }

    const spinner = ora('🔍 扫描项目文件...').start();
    
    try {
      const scan = await this.context.scanner.scanDirectory(this.context.projectPath);
      const summary = this.context.scanner.generateSummary(scan);
      
      spinner.text = '💾 保存扫描结果...';
      await this.context.memory.addScanResult(scan, summary);
      
      spinner.stop();
      
      this.context.isProjectInitialized = true;
      
      console.log(chalk.green('✅ 项目初始化完成！'));
      console.log(chalk.blue('📊 扫描结果:'));
      console.log(`  📁 总文件数: ${scan.totalFiles}`);
      console.log(`  📏 总大小: ${(scan.totalSize / (1024 * 1024)).toFixed(2)} MB`);
      console.log(`  📝 记忆文件: ${this.context.memory.getMemoryFile()}\n`);
    } catch (error) {
      spinner.stop();
      console.log(chalk.red(`❌ 初始化失败: ${error instanceof Error ? error.message : '未知错误'}`));
    }
  }

  private async handleConfig(): Promise<void> {
    console.log(chalk.blue('⚙️  配置 API 密钥\n'));
    
    const currentConfig = this.context.config.getConfig();
    const choices = [
      { name: '设置 DeepSeek API 密钥', value: 'deepseek' },
      { name: '设置 OpenAI API 密钥', value: 'openai' },
      { name: '设置 Anthropic API 密钥', value: 'anthropic' },
      { name: '查看当前配置', value: 'view' },
      { name: '返回', value: 'back' }
    ];

    const { action } = await inquirer.prompt([
      {
        type: 'list',
        name: 'action',
        message: '选择操作:',
        choices
      }
    ]);

    if (action === 'back') {
      return;
    }

    if (action === 'view') {
      console.log(chalk.blue('\n📋 当前配置:'));
      console.log(`  默认模型: ${currentConfig.defaultModel}`);
      console.log(`  DeepSeek API: ${currentConfig.apiKeys.deepseek ? '已设置' : '未设置'}`);
      console.log(`  OpenAI API: ${currentConfig.apiKeys.openai ? '已设置' : '未设置'}`);
      console.log(`  Anthropic API: ${currentConfig.apiKeys.anthropic ? '已设置' : '未设置'}`);
      console.log(`  配置文件: ${this.context.config.getConfigPath()}\n`);
      return;
    }

    if (action === 'deepseek' || action === 'openai' || action === 'anthropic') {
      const { apiKey } = await inquirer.prompt([
        {
          type: 'password',
          name: 'apiKey',
          message: `输入 ${action.toUpperCase()} API 密钥:`,
          mask: '*'
        }
      ]);

      if (apiKey.trim()) {
        this.context.config.setApiKey(action as any, apiKey.trim());
        console.log(chalk.green(`✅ ${action.toUpperCase()} API 密钥已保存\n`));
        
        // 重新初始化聊天管理器以应用新配置
        this.context.chat = new ChatManager(this.context.config);
      }
    }
  }

  private async handleModels(): Promise<void> {
    const availableModels = this.context.chat.getAvailableModels();
    const allModels = Object.keys(this.context.config.getConfig().models);
    
    console.log(chalk.blue('🤖 模型状态\n'));
    
    allModels.forEach(model => {
      const modelConfig = this.context.config.getConfig().models[model];
      const isAvailable = availableModels.includes(model);
      const status = isAvailable ? chalk.green('✅ 可用') : chalk.red('❌ 不可用');
      
      console.log(`${model.padEnd(20)} ${modelConfig.provider.padEnd(10)} ${status}`);
    });
    console.log();
  }

  private async handleMemory(): Promise<void> {
    if (!this.context.isProjectInitialized) {
      console.log(chalk.yellow('📝 项目记忆未初始化'));
      console.log(chalk.gray('💡 使用 "init" 命令初始化项目记忆\n'));
      return;
    }

    const recentEntries = await this.context.memory.getRecentEntries(5);
    console.log(chalk.blue('🧠 项目记忆状态'));
    console.log(`📂 记忆文件: ${this.context.memory.getMemoryFile()}`);
    console.log(`📄 记录条目: ${recentEntries.length} 条\n`);
    
    if (recentEntries.length > 0) {
      console.log(chalk.blue('最近的记录:'));
      recentEntries.forEach(entry => {
        const icon = entry.type === 'scan' ? '🔍' : 
                    entry.type === 'insight' ? '💡' : 
                    entry.type === 'todo' ? '📝' : '💬';
        console.log(`  ${icon} ${entry.timestamp.toLocaleDateString()} - ${entry.content.slice(0, 60)}...`);
      });
    }
    console.log();
  }

  private async handleInsight(args: string[]): Promise<void> {
    const insight = args.join(' ').trim();
    if (!insight) {
      console.log(chalk.red('❌ 请提供洞察内容'));
      console.log(chalk.gray('例如: insight 这个项目使用了微服务架构\n'));
      return;
    }

    await this.context.chat.addInsight(insight);
    console.log(chalk.green('✅ 洞察已记录\n'));
  }

  private async handleTodo(args: string[]): Promise<void> {
    const todo = args.join(' ').trim();
    if (!todo) {
      console.log(chalk.red('❌ 请提供待办内容'));
      console.log(chalk.gray('例如: todo 优化数据库查询性能\n'));
      return;
    }

    await this.context.chat.addTodo(todo);
    console.log(chalk.green('✅ 待办事项已记录\n'));
  }

  private async handleClear(): Promise<void> {
    console.clear();
    this.showWelcome();
  }

  private async handleExit(): Promise<void> {
    console.log(chalk.yellow('👋 再见！'));
    this.running = false;
  }

  private cleanup(): void {
    // 清理资源
    console.log(chalk.gray('正在清理资源...'));
  }

  private async handlePwd(): Promise<void> {
    console.log(this.context.projectPath);
  }

  private async handleLs(args: string[]): Promise<void> {
    await this.handleSystemCommand(`ls ${args.join(' ')}`);
  }

  private async handleShortcuts(): Promise<void> {
    console.log(this.shortcutParser.getShortcutHelp());
  }

  private async handleHistory(): Promise<void> {
    const history = this.smartInquirer.getHistory();
    
    if (history.length === 0) {
      console.log(chalk.yellow('📝 暂无命令历史'));
      return;
    }

    console.log(chalk.blue('\n📝 命令历史:'));
    history.slice(0, 20).forEach((cmd, index) => {
      const timeAgo = index === 0 ? '刚刚' : `${index + 1} 条前`;
      console.log(`  ${chalk.gray(timeAgo.padEnd(8))} ${cmd}`);
    });
    
    if (history.length > 20) {
      console.log(chalk.gray(`  ... 还有 ${history.length - 20} 条历史记录`));
    }
    console.log();
  }

  private async handleStats(): Promise<void> {
    this.smartInquirer.showInputStats();
  }

  private async handleTask(): Promise<void> {
    console.log(chalk.blue('\n📋 任务管理系统\n'));
    
    const choices = [
      { name: '📝 创建新任务', value: 'create' },
      { name: '📋 查看任务列表', value: 'list' },
      { name: '🎯 管理任务', value: 'manage' },
      { name: '📊 任务统计', value: 'stats' },
      { name: '🔍 搜索任务', value: 'search' },
      { name: '↩️  返回', value: 'back' }
    ];

    try {
      const { action } = await inquirer.prompt([
        {
          type: 'list',
          name: 'action',
          message: '选择操作:',
          choices
        }
      ]);

      switch (action) {
        case 'create':
          await this.handleCreateTask();
          break;
        case 'list':
          await this.taskInteraction.showTaskList();
          break;
        case 'manage':
          await this.handleManageTask();
          break;
        case 'stats':
          this.taskInteraction.showTaskStatistics();
          break;
        case 'search':
          await this.taskInteraction.showTaskSearch();
          break;
        case 'back':
          return;
      }
    } catch (error) {
      console.log(chalk.red('❌ 任务系统操作失败'));
    }
  }

  private async handleCreateTask(): Promise<void> {
    const task = await this.taskInteraction.showCreateTaskInterface();
    if (task) {
      console.log(chalk.green(`✅ 任务创建成功: ${task.title}`));
      console.log(chalk.gray(`📋 任务ID: ${task.id.slice(0, 8)}...`));
      
      if (task.status === 'ready') {
        const { executeNow } = await inquirer.prompt([
          {
            type: 'confirm',
            name: 'executeNow',
            message: '是否立即执行这个任务?',
            default: false
          }
        ]);

        if (executeNow) {
          await this.handleExecuteTask(task.id);
        }
      }
    }
  }

  private async handleManageTask(): Promise<void> {
    let continueManaging = true;
    
    while (continueManaging) {
      const selectedTaskId = await this.taskInteraction.showTaskMenu();
      
      if (!selectedTaskId || selectedTaskId === 'back') {
        continueManaging = false;
        break;
      }
      
      if (selectedTaskId === 'stats') {
        this.taskInteraction.showTaskStatistics();
        continue;
      }
      
      if (selectedTaskId === 'search') {
        await this.taskInteraction.showTaskSearch();
        continue;
      }
      
      // 处理具体任务
      await this.handleTaskActions(selectedTaskId);
    }
  }

  private async handleTaskActions(taskId: string): Promise<void> {
    let continueActions = true;
    
    while (continueActions) {
      const action = await this.taskInteraction.showTaskActions(taskId);
      
      if (!action || action === 'back') {
        continueActions = false;
        break;
      }
      
      switch (action) {
        case 'execute':
          await this.handleExecuteTask(taskId);
          break;
        case 'pause':
          this.taskManager.pauseTask(taskId);
          break;
        case 'resume':
          this.taskManager.resumeTask(taskId);
          break;
        case 'cancel':
          this.taskManager.cancelTask(taskId);
          break;
        case 'details':
          await this.taskInteraction.showTaskDetails(taskId);
          break;
        case 'note':
          await this.taskInteraction.addTaskNote(taskId);
          break;
        case 'logs':
          await this.taskInteraction.showTaskDetails(taskId);
          break;
      }
    }
  }

  private async handleExecuteTask(taskId: string): Promise<void> {
    try {
      console.log(chalk.blue('🚀 开始执行任务...'));
      const result = await this.taskManager.executeTask(taskId);
      
      if (result.success) {
        console.log(chalk.green('✅ 任务执行成功！'));
        console.log(chalk.blue('📋 执行结果:'));
        console.log(result.result);
      } else {
        console.log(chalk.red('❌ 任务执行失败'));
        console.log(chalk.yellow('💡 你可以查看任务详情了解失败原因'));
      }
    } catch (error) {
      console.log(chalk.red(`❌ 任务执行异常: ${error instanceof Error ? error.message : '未知错误'}`));
    }
  }

  private async handleFilePath(result: ShortcutResult): Promise<void> {
    const { metadata } = result;
    
    if (!metadata) {
      console.log(chalk.yellow('📁 文件路径查询，使用格式: @文件路径 [操作说明]'));
      console.log(chalk.gray('例如: @package.json 分析这个文件的依赖'));
      return;
    }

    if (metadata.error) {
      console.log(chalk.red(`❌ ${metadata.error}`));
      return;
    }

    // 检查用户是否想要与 AI 交互分析文件
    const originalInput = result.original.trim();
    const hasAdditionalRequest = originalInput.includes(' ') && originalInput.split(' ').length > 1;
    
    if (hasAdditionalRequest) {
      // 用户想要 AI 帮助分析文件
      const parts = originalInput.split(' ');
      const filePath = parts[0].slice(1); // 移除 @
      const userRequest = parts.slice(1).join(' ');
      
      await this.handleFileWithAI(filePath, userRequest);
      return;
    }

    // 传统的文件信息显示
    if (metadata.found && metadata.fileInfo) {
      const { fileInfo } = metadata;
      console.log(chalk.blue('\n📄 文件信息:'));
      console.log(`  路径: ${fileInfo.relativePath}`);
      console.log(`  完整路径: ${fileInfo.path}`);
      console.log(`  类型: ${fileInfo.isFile ? '文件' : '目录'}`);
      if (fileInfo.isFile) {
        console.log(`  大小: ${(fileInfo.size / 1024).toFixed(2)} KB`);
      }
      console.log(`  修改时间: ${fileInfo.modified.toLocaleString('zh-CN')}`);
      
      // 提示用户可以进行 AI 交互
      console.log(chalk.gray('\n💡 提示: 使用 "@文件路径 请求" 让 AI 帮助分析文件'));
      console.log(chalk.gray('例如: @package.json 帮我分析这个项目的依赖'));
      console.log();
      return;
    }

    if (metadata.isGlob && metadata.matches) {
      console.log(chalk.blue(`\n🔍 匹配结果 (共 ${metadata.totalMatches} 个):`));
      metadata.matches.forEach((match: string) => {
        console.log(`  📄 ${match}`);
      });
      if (metadata.totalMatches > metadata.matches.length) {
        console.log(chalk.gray(`  ... 还有 ${metadata.totalMatches - metadata.matches.length} 个文件`));
      }
      console.log(chalk.gray('\n💡 提示: 选择具体文件进行 AI 分析'));
      console.log();
    }
  }

  private async handleFileWithAI(filePath: string, userRequest: string): Promise<void> {
    // 检查是否有可用模型
    const availableModels = this.context.chat.getAvailableModels();
    if (availableModels.length === 0) {
      console.log(chalk.red('❌ 无可用模型，请先使用 "config" 命令配置 API 密钥'));
      return;
    }

    console.log(chalk.blue(`🔍 正在分析文件: ${filePath}`));
    console.log(chalk.gray(`📝 用户请求: ${userRequest}`));

    // 构建完整的 AI 请求
    const aiRequest = `请使用 smart_file_operation 工具来帮我处理文件操作需求：

文件路径: ${filePath}
用户需求: ${userRequest}

请根据用户需求选择合适的操作类型（read_for_analysis, write_ai_content, edit_with_ai, analyze_structure, list_and_suggest）并执行相应的文件操作。`;

    // 发送给 AI 处理
    await this.handleChat(aiRequest);
  }

  private async handleMemoryShortcut(result: ShortcutResult): Promise<void> {
    const { metadata } = result;
    
    if (!metadata || !metadata.action) {
      await this.handleMemory();
      return;
    }

    switch (metadata.action) {
      case 'view':
        await this.handleMemory();
        break;
      case 'insight':
        if (metadata.args && metadata.args.length > 0) {
          await this.context.chat.addInsight(metadata.args.join(' '));
          console.log(chalk.green('✅ 洞察已记录\n'));
        } else {
          console.log(chalk.red('❌ 请提供洞察内容\n'));
        }
        break;
      case 'todo':
        if (metadata.args && metadata.args.length > 0) {
          await this.context.chat.addTodo(metadata.args.join(' '));
          console.log(chalk.green('✅ 待办事项已记录\n'));
        } else {
          console.log(chalk.red('❌ 请提供待办内容\n'));
        }
        break;
      case 'search':
        console.log(chalk.yellow('🔍 记忆搜索功能开发中...'));
        break;
      case 'clear':
        console.log(chalk.yellow('🗑️  记忆清除功能开发中...'));
        break;
      default:
        await this.handleMemory();
        break;
    }
  }

  /**
   * 启动现代化界面
   */
  private async startModernInterface(): Promise<void> {
    try {
      // 动态导入现代化UI组件
      const { startModernInterface } = await import('../ui');
      await startModernInterface(this.context);
    } catch (error) {
      this.handleModernUIError(error);

      // 回退到传统界面
      this.useModernUI = false;
      this.showWelcome();

      // 启动传统交互循环
      await this.runInteractiveLoop();
    }
  }

  /**
   * 运行交互循环（公共方法）
   */
  private async runInteractiveLoop(): Promise<void> {
    while (this.running) {
      try {
        const input = await this.smartInquirer.createSmartInput(this.getPrompt());
        const trimmedInput = input.trim();

        if (trimmedInput) {
          await this.processInput(trimmedInput);
        }
      } catch (error) {
        if (error instanceof Error && error.name === 'ExitPromptError') {
          console.log(chalk.yellow('\n👋 再见！'));
          break;
        }
        console.log(chalk.red(`❌ 错误: ${error instanceof Error ? error.message : '未知错误'}`));
      }
    }
  }

  /**
   * 处理现代化UI启动错误
   */
  private handleModernUIError(error: unknown): void {
    if (error instanceof Error) {
      // 根据错误类型提供具体的错误信息
      const nodeError = error as Error & { code?: string };
      if (nodeError.code === 'MODULE_NOT_FOUND') {
        console.log(chalk.yellow('⚠️  现代化界面组件未安装，使用传统界面'));
        console.log(chalk.gray('提示: 运行 npm install 确保所有依赖已安装'));
      } else if (error.message.includes('permission')) {
        console.log(chalk.red('❌ 权限不足，无法启动现代化界面'));
        console.log(chalk.gray('提示: 检查文件权限或使用管理员权限运行'));
      } else if (error.message.includes('ENOENT')) {
        console.log(chalk.red('❌ 找不到必要的文件，无法启动现代化界面'));
        console.log(chalk.gray('提示: 确认项目文件完整'));
      } else if (error.message.includes('syntax') || error.message.includes('Unexpected')) {
        console.log(chalk.red('❌ 代码语法错误，无法启动现代化界面'));
        console.log(chalk.gray('提示: 检查UI组件代码语法'));
      } else {
        console.log(chalk.red('❌ 现代化界面启动失败，回退到传统界面'));
        console.log(chalk.gray(`详细错误: ${error.message}`));
      }
    } else {
      console.log(chalk.red('❌ 现代化界面启动失败，回退到传统界面'));
      console.log(chalk.gray('未知错误类型'));
    }
  }

}