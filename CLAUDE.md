# CLAUDE.md

This file provides guidance to <PERSON> (claude.ai/code) when working with code in this repository.

## Project Overview

Chater 是一个基于 LangChain 的智能 AI 聊天代理 CLI 工具，支持多种 AI 模型（DeepSeek、OpenAI、Anthropic）。

## 核心架构

### 三层架构设计
- **CLI 层**: `src/cli.ts` - Commander.js 实现的命令行界面，支持传统命令和交互式模式
- **核心业务层**: 
  - `src/core/chat.ts` - 智能聊天管理，实现自动模型选择算法
  - `src/core/config.ts` - cosmiconfig 配置管理，支持多种配置格式
  - `src/core/interactive.ts` - 统一交互式环境，智能命令解析
  - `src/core/memory.ts` - 项目记忆系统，持久化项目上下文
  - `src/core/toolManager.ts` - 工具系统管理器，注册和执行各种开发工具
- **适配器层**: 
  - `src/models/` - AI 服务商适配器，统一 ModelProvider 接口
  - `src/tools/` - 开发工具集成，包含 git、npm、changelog 等工具
  - `src/utils/` - 工具函数，包括文件扫描器等

### 类型系统
- `src/types/index.ts` - 核心 TypeScript 接口定义，包括 ChatMessage、Tool、ModelProvider 等

## 开发命令

```bash
# 开发模式运行（使用 ts-node）
npm run dev [command] [options]

# 构建项目
npm run build

# 代码质量检查
npm run lint

# 运行测试
npm test

# 启动交互式开发环境
npm run dev
# 或者直接运行特定命令
npm run dev chat
npm run dev init
npm run dev config
```

## 智能模型选择机制

项目实现了智能模型选择功能（`src/core/chat.ts:75-139`）：
- **复杂度分析算法**: 基于关键词匹配、文本长度、标点复杂度等多维度指标
- **智能路由策略**: 
  - 简单问题（查询、帮助类）→ `deepseek-chat`（快速响应）
  - 复杂问题（分析、推理、设计类）→ `deepseek-reasoner`（深度思考）
- **动态评分系统**: 复杂关键词 +2 分，简单关键词 -1 分，长度和多问题额外加分

## 配置管理

- 配置文件位置：`~/.chaterrc.json`
- 支持 cosmiconfig 格式（`.chaterrc.js`, `chater.config.js` 等）
- API 密钥存储在用户配置中，通过 `chater config` 命令设置

## 模型提供商架构

所有 AI 服务商都实现统一的 `ModelProvider` 接口（`src/types/index.ts:60-68`）：
- `chat(messages: ChatMessage[], tools?: Tool[]): Promise<{content: string, toolCalls?: ToolCall[]}>` - 支持工具调用的聊天接口
- `isAvailable(): boolean` - 检查 API 密钥和服务可用性
- `supportsTools(): boolean` - 检查是否支持工具调用
- 使用 LangChain 统一消息格式，确保跨模型兼容性

## 工具系统架构

### 工具管理器 (`src/core/toolManager.ts`)
- 统一工具注册和执行机制
- 支持工具调用历史记录
- 自动加载预定义工具集

### 预置工具集 (`src/tools/`)
- **Git 工具**: `gitTool.ts` - 版本控制操作
- **NPM 工具**: `npmTool.ts` - 包管理和依赖分析  
- **智能提交**: `commitMsgGenerator.ts` - 基于 diff 生成提交信息
- **变更日志**: `changelogTool.ts` - 自动生成和维护 CHANGELOG

## CLI 命令结构

### CLI 命令设计

**主要命令**:
- `chater` - 默认启动交互式开发环境（推荐使用）
- `chater chat` - 启动传统聊天模式（仅对话）
- `chater init` - 初始化项目：扫描文件结构并创建记忆文件
- `chater config` - 配置 API 密钥
- `chater models` - 查看可用模型

### 交互式环境特性

**三级智能命令解析** (`src/core/interactive.ts:190-211`):
1. **内置命令优先**: help, init, config 等核心命令
2. **系统命令识别**: 自动识别并执行 git, npm, docker 等开发工具
3. **AI 对话降级**: 其他输入作为 AI 对话处理

**核心交互命令**:
- `help` - 显示帮助信息和所有可用命令
- `init` - 扫描项目文件结构，创建 `CHATER.md` 记忆文件
- `config` - 交互式配置 API 密钥管理
- `models` - 显示所有模型的可用状态
- `memory` - 查看项目记忆状态和最近记录
- `insight <内容>` - 添加重要项目洞察到记忆系统
- `todo <内容>` - 记录待办事项
- `clear` - 清屏并重新显示欢迎信息
- `exit` - 退出交互环境

**开发工具集成**:
- 直接执行系统命令: `git status`, `npm install`, `docker ps`
- 工作目录感知，命令在项目根目录执行
- 实时显示命令输出和错误信息

## 项目记忆系统

### 记忆系统架构
- **FileScanner** (`src/utils/fileScanner.ts`) - 递归扫描项目文件，分析结构、权限和文件类型统计
- **MemoryManager** (`src/core/memory.ts`) - 管理 `CHATER.md` 记忆文件，支持结构化存储和检索
- **记忆集成** (`src/core/chat.ts`) - 将项目记忆作为系统上下文自动注入 AI 对话

### 记忆文件机制
- **存储位置**: 项目根目录的 `CHATER.md` 文件
- **内容结构**: 项目扫描报告、对话摘要、用户洞察、待办事项
- **上下文注入**: AI 对话时自动加载，提供项目感知能力
- **增量更新**: 支持动态添加洞察和待办事项

### 工作流集成特性
- **无缝切换**: AI 对话 ↔ 系统命令 ↔ 项目管理，无需模式切换
- **上下文保持**: 项目信息在整个会话中持续可用
- **智能建议**: 基于项目结构和历史对话提供精准建议

## 关键修复说明

已修复的问题：
- ✅ 统一版本号到 0.3.0（`package.json`、`src/cli.ts:78`、`src/core/interactive.ts:165`）
- ✅ 移除 `package.json` 中的循环依赖 `"chater": "^0.2.1"`
- ✅ 升级到 ESLint v9 并创建新的 `eslint.config.js` 配置文件
- ✅ 配置适合 CLI 工具的代码检查规则，允许 console 输出，适度的类型检查
- ✅ 修复 punycode 弃用警告（通过 `--no-deprecation` 选项）
- ✅ 优化工具注册日志，避免重复显示

## 新功能：快捷键系统

### 快捷键解析器 (`src/utils/shortcutParser.ts`)
- **智能输入解析**: 根据前缀字符自动识别用户意图
- **四类快捷键支持**: 命令(/)、bash(!)、文件(@)、记忆(#)
- **文件路径智能匹配**: 支持 glob 模式和完整路径解析

### 快捷键功能
- **/ 命令快捷键**: `/help`、`/config`、`/models` 等
- **! bash 快捷键**: `!git status`、`!npm install` 等
- **@ 文件快捷键**: `@package.json`、`@src/*.ts` 等，支持文件信息显示和 glob 匹配
- **# 记忆快捷键**: `#重要发现`、`#todo 任务`、`#view` 等

### 集成到交互式环境
- **统一输入处理**: 在 `InteractiveCLI` 中集成快捷键解析
- **智能路由**: 根据解析结果自动路由到相应处理器
- **帮助系统**: 通过 `shortcuts` 命令查看详细快捷键帮助

## 高级输入功能

### 自动补全和建议系统 (`src/utils/advancedInput.ts`)
- **智能建议**: 根据输入前缀提供相关命令、文件、bash 命令建议
- **实时计数器**: 输入时显示可用建议数量
- **文件路径补全**: 支持目录遍历和 glob 模式匹配
- **命令历史**: 自动保存和加载历史命令到 `.chater_history`

### 可视化建议界面 (`src/utils/suggestionDisplay.ts`)
- **快捷键面板**: 输入 `?` 显示美观的快捷键参考面板
- **彩色建议**: 不同类型的建议使用不同颜色区分
- **智能提示**: 根据输入内容显示相关建议列表

### 交互体验增强
- **历史命令**: `history` 命令查看最近 20 条命令历史
- **实时反馈**: 输入时显示建议数量和类型提示
- **特殊命令**: `?` 显示快捷键面板，支持重复输入
- **ESC 中断**: AI 思考过程中按 ESC 键可立即中断请求

## AI 思考中断系统

### 键盘中断处理器 (`src/utils/keyboardInterrupt.ts`)
- **键盘监听**: 在 AI 思考时启用原始键盘模式监听 ESC 键
- **AbortController 集成**: 使用标准 Web API 中断 HTTP 请求
- **优雅清理**: 自动清理键盘监听器，避免资源泄漏

### 中断管理器功能
- **ThinkingInterruptManager**: 管理思考状态和中断逻辑
- **实时提示**: 思考开始 1 秒后显示中断提示
- **状态管理**: 跟踪思考状态，防止重复监听

### 集成到 AI 对话流程
- **ChatManager 支持**: `sendMessage` 方法支持 AbortController 参数
- **对话循环中断**: 在 AI 调用和工具执行的关键点检查中断状态
- **用户友好反馈**: 中断时显示清晰的提示信息