import { cosmiconfigSync } from 'cosmiconfig';
import { ChaterConfig } from '../types';
import * as path from 'path';
import * as fs from 'fs';
import * as os from 'os';

const moduleName = 'chater';
const explorer = cosmiconfigSync(moduleName);

const defaultConfig: ChaterConfig = {
  defaultModel: 'auto',
  apiKeys: {},
  models: {
    'deepseek-chat': {
      provider: 'deepseek',
      model: 'deepseek-chat',
      temperature: 0.7,
      maxTokens: 2000
    },
    'deepseek-reasoner': {
      provider: 'deepseek',
      model: 'deepseek-reasoner',
      temperature: 0.7,
      maxTokens: 2000
    },
    'deepseek-coder': {
      provider: 'deepseek',
      model: 'deepseek-coder',
      temperature: 0.7,
      maxTokens: 2000
    },
    'gpt-3.5-turbo': {
      provider: 'openai',
      model: 'gpt-3.5-turbo',
      temperature: 0.7,
      maxTokens: 2000
    },
    'gpt-4': {
      provider: 'openai',
      model: 'gpt-4',
      temperature: 0.7,
      maxTokens: 2000
    },
    'claude-3-sonnet': {
      provider: 'anthropic',
      model: 'claude-3-sonnet-20240229',
      temperature: 0.7,
      maxTokens: 2000
    }
  },
  storage: {
    historyLimit: 50,
    sessionLimit: 10
  }
};

export class ConfigManager {
  private config: ChaterConfig;
  private configPath: string;

  constructor() {
    this.configPath = path.join(os.homedir(), '.chaterrc.json');
    this.config = this.loadConfig();
  }

  private loadConfig(): ChaterConfig {
    try {
      // 首先尝试从用户配置文件加载
      const result = explorer.search();
      if (result && result.config) {
        return { ...defaultConfig, ...result.config };
      }

      // 如果没有找到配置文件，检查是否存在默认配置文件
      if (fs.existsSync(this.configPath)) {
        const userConfig = JSON.parse(fs.readFileSync(this.configPath, 'utf8'));
        return { ...defaultConfig, ...userConfig };
      }

      // 创建默认配置文件
      this.saveConfig(defaultConfig);
      return defaultConfig;
    } catch (error) {
      console.warn('配置加载失败，使用默认配置:', error);
      return defaultConfig;
    }
  }

  public getConfig(): ChaterConfig {
    return this.config;
  }

  public updateConfig(updates: Partial<ChaterConfig>): void {
    this.config = { ...this.config, ...updates };
    this.saveConfig(this.config);
  }

  public setApiKey(provider: keyof ChaterConfig['apiKeys'], key: string): void {
    this.config.apiKeys[provider] = key;
    this.saveConfig(this.config);
  }

  private saveConfig(config: ChaterConfig): void {
    try {
      fs.writeFileSync(this.configPath, JSON.stringify(config, null, 2));
    } catch (error) {
      console.error('配置保存失败:', error);
    }
  }

  public getConfigPath(): string {
    return this.configPath;
  }
}