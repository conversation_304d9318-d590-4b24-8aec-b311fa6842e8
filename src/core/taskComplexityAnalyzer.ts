import { TaskComplexity, TaskComplexityAnalysis, ComplexityFactor, TaskCategory } from '../types/task';

export class TaskComplexityAnalyzer {
  
  /**
   * 分析任务复杂度
   */
  public analyzeComplexity(description: string, context?: any): TaskComplexityAnalysis {
    const factors = this.calculateComplexityFactors(description, context);
    const totalScore = this.calculateTotalScore(factors);
    const complexity = this.determineComplexity(totalScore);
    
    return {
      complexity,
      score: totalScore,
      factors,
      recommendation: this.generateRecommendation(complexity, factors),
      requiresDecomposition: this.shouldDecompose(complexity, totalScore),
      estimatedTime: this.estimateTime(complexity, factors)
    };
  }

  /**
   * 计算复杂度因素
   */
  private calculateComplexityFactors(description: string, context?: any): ComplexityFactor[] {
    const factors: ComplexityFactor[] = [];
    const text = description.toLowerCase();

    // 1. 任务范围因素
    factors.push(this.analyzeScopeComplexity(text));
    
    // 2. 技术复杂度因素
    factors.push(this.analyzeTechnicalComplexity(text));
    
    // 3. 依赖复杂度因素
    factors.push(this.analyzeDependencyComplexity(text, context));
    
    // 4. 交互复杂度因素
    factors.push(this.analyzeInteractionComplexity(text));
    
    // 5. 时间复杂度因素
    factors.push(this.analyzeTimeComplexity(text));
    
    // 6. 风险复杂度因素
    factors.push(this.analyzeRiskComplexity(text));

    return factors;
  }

  /**
   * 分析任务范围复杂度
   */
  private analyzeScopeComplexity(text: string): ComplexityFactor {
    let score = 20; // 基础分数
    
    // 多个动作关键词
    const actionKeywords = ['创建', '修改', '删除', '优化', '重构', '分析', '测试', '部署', '集成'];
    const actionCount = actionKeywords.filter(keyword => text.includes(keyword)).length;
    score += actionCount * 10;
    
    // 多个对象关键词
    const objectKeywords = ['文件', '组件', '模块', '系统', '数据库', '接口', '服务', '工具'];
    const objectCount = objectKeywords.filter(keyword => text.includes(keyword)).length;
    score += objectCount * 8;
    
    // 范围指示词
    const scopeKeywords = [
      { words: ['整个', '全部', '所有', '完整'], weight: 15 },
      { words: ['多个', '几个', '一些'], weight: 10 },
      { words: ['复杂', '大型', '完善'], weight: 12 }
    ];
    
    scopeKeywords.forEach(group => {
      if (group.words.some(word => text.includes(word))) {
        score += group.weight;
      }
    });

    return {
      name: '任务范围',
      score: Math.min(score, 100),
      weight: 0.25,
      description: `涉及 ${actionCount} 个动作，${objectCount} 个对象`
    };
  }

  /**
   * 分析技术复杂度
   */
  private analyzeTechnicalComplexity(text: string): ComplexityFactor {
    let score = 15;
    
    // 高复杂度技术关键词
    const highComplexityTech = [
      '架构', '设计模式', '算法', '性能优化', '安全', '并发', '分布式',
      '微服务', 'docker', 'kubernetes', '数据库设计', '缓存', '消息队列'
    ];
    
    // 中等复杂度技术关键词
    const mediumComplexityTech = [
      'api', '接口', '组件', '模块', '插件', '工具', '脚本', '配置',
      'typescript', 'react', 'vue', 'node.js', '测试'
    ];
    
    // 低复杂度技术关键词
    const lowComplexityTech = [
      '文档', '注释', '格式化', '样式', 'css', 'html', '静态',
      '复制', '移动', '重命名'
    ];

    const highCount = highComplexityTech.filter(tech => text.includes(tech)).length;
    const mediumCount = mediumComplexityTech.filter(tech => text.includes(tech)).length;
    const lowCount = lowComplexityTech.filter(tech => text.includes(tech)).length;
    
    score += highCount * 20 + mediumCount * 10 + lowCount * 5;

    return {
      name: '技术复杂度',
      score: Math.min(score, 100),
      weight: 0.3,
      description: `高复杂度技术 ${highCount} 个，中等 ${mediumCount} 个，低复杂度 ${lowCount} 个`
    };
  }

  /**
   * 分析依赖复杂度
   */
  private analyzeDependencyComplexity(text: string, context?: any): ComplexityFactor {
    let score = 10;
    
    // 外部依赖关键词
    const externalDeps = [
      '第三方', '外部', 'api', '服务', '数据库', '文件系统', '网络',
      '安装', '配置', '环境', '依赖'
    ];
    
    // 内部依赖关键词
    const internalDeps = [
      '其他模块', '组件', '函数', '类', '接口', '工具'
    ];

    const externalCount = externalDeps.filter(dep => text.includes(dep)).length;
    const internalCount = internalDeps.filter(dep => text.includes(dep)).length;
    
    score += externalCount * 15 + internalCount * 8;
    
    // 如果有上下文信息，分析文件依赖
    if (context?.relevantFiles?.length > 5) {
      score += 20;
    } else if (context?.relevantFiles?.length > 0) {
      score += 10;
    }

    return {
      name: '依赖复杂度',
      score: Math.min(score, 100),
      weight: 0.2,
      description: `外部依赖 ${externalCount} 个，内部依赖 ${internalCount} 个`
    };
  }

  /**
   * 分析交互复杂度
   */
  private analyzeInteractionComplexity(text: string): ComplexityFactor {
    let score = 5;
    
    // 用户交互关键词
    const userInteractionKeywords = [
      '用户', '界面', 'ui', 'ux', '交互', '输入', '输出', '验证',
      '确认', '选择', '配置'
    ];
    
    // 系统交互关键词
    const systemInteractionKeywords = [
      '集成', '同步', '通信', '协调', '监控', '日志', '错误处理'
    ];

    const userCount = userInteractionKeywords.filter(keyword => text.includes(keyword)).length;
    const systemCount = systemInteractionKeywords.filter(keyword => text.includes(keyword)).length;
    
    score += userCount * 12 + systemCount * 10;

    return {
      name: '交互复杂度',
      score: Math.min(score, 100),
      weight: 0.15,
      description: `用户交互 ${userCount} 个，系统交互 ${systemCount} 个`
    };
  }

  /**
   * 分析时间复杂度
   */
  private analyzeTimeComplexity(text: string): ComplexityFactor {
    let score = 10;
    
    // 时间相关关键词
    const timeKeywords = [
      { words: ['紧急', '立即', '马上'], weight: 25 },
      { words: ['尽快', '及时', '快速'], weight: 15 },
      { words: ['逐步', '分阶段', '渐进'], weight: 20 },
      { words: ['长期', '持续', '维护'], weight: 30 }
    ];
    
    timeKeywords.forEach(group => {
      if (group.words.some(word => text.includes(word))) {
        score += group.weight;
      }
    });
    
    // 规模指示词
    if (text.includes('大量') || text.includes('批量')) {
      score += 20;
    }

    return {
      name: '时间复杂度',
      score: Math.min(score, 100),
      weight: 0.1,
      description: score > 30 ? '时间敏感或规模较大' : '时间要求一般'
    };
  }

  /**
   * 分析风险复杂度
   */
  private analyzeRiskComplexity(text: string): ComplexityFactor {
    let score = 5;
    
    // 高风险关键词
    const highRiskKeywords = [
      '删除', '移除', '重构', '迁移', '升级', '部署', '生产',
      '数据库', '安全', '权限', '破坏性'
    ];
    
    // 中风险关键词
    const mediumRiskKeywords = [
      '修改', '更新', '优化', '测试', '验证', '配置'
    ];

    const highRiskCount = highRiskKeywords.filter(keyword => text.includes(keyword)).length;
    const mediumRiskCount = mediumRiskKeywords.filter(keyword => text.includes(keyword)).length;
    
    score += highRiskCount * 20 + mediumRiskCount * 10;

    return {
      name: '风险复杂度',
      score: Math.min(score, 100),
      weight: 0.15,
      description: `高风险操作 ${highRiskCount} 个，中风险操作 ${mediumRiskCount} 个`
    };
  }

  /**
   * 计算总分
   */
  private calculateTotalScore(factors: ComplexityFactor[]): number {
    const weightedSum = factors.reduce((sum, factor) => 
      sum + (factor.score * factor.weight), 0
    );
    return Math.round(weightedSum);
  }

  /**
   * 根据分数确定复杂度等级
   */
  private determineComplexity(score: number): TaskComplexity {
    if (score >= 75) return 'very_complex';
    if (score >= 50) return 'complex';
    if (score >= 25) return 'moderate';
    return 'simple';
  }

  /**
   * 生成建议
   */
  private generateRecommendation(complexity: TaskComplexity, factors: ComplexityFactor[]): string {
    const highestFactor = factors.reduce((max, factor) => 
      factor.score > max.score ? factor : max
    );

    switch (complexity) {
      case 'very_complex':
        return `这是一个非常复杂的任务，建议使用 deepseek-reasoner 进行详细规划和分解。主要复杂因素：${highestFactor.name}`;
      case 'complex':
        return `这是一个复杂任务，建议进行任务分解。关注点：${highestFactor.name}`;
      case 'moderate':
        return `中等复杂度任务，可以考虑简单分解或直接执行。注意：${highestFactor.name}`;
      case 'simple':
        return `简单任务，可以直接使用 deepseek-chat 执行`;
      default:
        return '复杂度分析完成';
    }
  }

  /**
   * 判断是否需要分解
   */
  private shouldDecompose(complexity: TaskComplexity, score: number): boolean {
    return complexity === 'complex' || complexity === 'very_complex' || score >= 45;
  }

  /**
   * 估算时间（分钟）
   */
  private estimateTime(complexity: TaskComplexity, factors: ComplexityFactor[]): number {
    const baseTime = {
      'simple': 10,
      'moderate': 30,
      'complex': 60,
      'very_complex': 120
    };

    let time = baseTime[complexity];
    
    // 根据具体因素调整
    factors.forEach(factor => {
      if (factor.score > 60) {
        time *= 1.5;
      } else if (factor.score > 40) {
        time *= 1.2;
      }
    });

    return Math.round(time);
  }

  /**
   * 分析任务类型
   */
  public analyzeTaskCategory(description: string): TaskCategory {
    const text = description.toLowerCase();
    
    const categoryKeywords = {
      'development': ['开发', '编写', '创建', '实现', '构建', '代码'],
      'analysis': ['分析', '研究', '调查', '评估', '检查', '审查'],
      'documentation': ['文档', '注释', '说明', '记录', '描述'],
      'debugging': ['调试', '修复', 'bug', '错误', '问题', '故障'],
      'optimization': ['优化', '改进', '提升', '性能', '效率'],
      'research': ['研究', '探索', '学习', '了解', '调研'],
      'maintenance': ['维护', '更新', '升级', '清理', '整理'],
      'deployment': ['部署', '发布', '上线', '安装', '配置'],
      'testing': ['测试', '验证', '检验', '试验'],
      'refactoring': ['重构', '重写', '改造', '重新设计']
    };

    let maxScore = 0;
    let category: TaskCategory = 'development';

    Object.entries(categoryKeywords).forEach(([cat, keywords]) => {
      const score = keywords.filter(keyword => text.includes(keyword)).length;
      if (score > maxScore) {
        maxScore = score;
        category = cat as TaskCategory;
      }
    });

    return category;
  }
}