import { ChatOpenAI } from '@langchain/openai';
import { HumanMessage, AIMessage, SystemMessage, ToolMessage } from '@langchain/core/messages';
import { ModelProvider, ChatMessage, Tool, ToolCall } from '../types';

export class DeepSeekProvider implements ModelProvider {
  public name = 'DeepSeek';
  private client: ChatOpenAI | null = null;

  constructor(
    private apiKey: string,
    private model: string = 'deepseek-chat',
    private temperature: number = 0.7,
    private maxTokens: number = 2000
  ) {
    if (this.isAvailable()) {
      this.client = new ChatOpenAI({
        openAIApiKey: this.apiKey,
        modelName: this.model,
        temperature: this.temperature,
        maxTokens: this.maxTokens,
        configuration: {
          baseURL: 'https://api.deepseek.com/v1',
        },
      });
    }
  }

  public isAvailable(): boolean {
    return !!this.apiKey;
  }

  public supportsTools(): boolean {
    // DeepSeek 支持 function calling
    return true;
  }

  public async chat(messages: ChatMessage[], tools?: Tool[]): Promise<{
    content: string;
    toolCalls?: ToolCall[];
  }> {
    if (!this.client) {
      throw new Error('DeepSeek provider is not available. Please check your API key.');
    }

    try {
      const langChainMessages = messages.map(msg => {
        switch (msg.role) {
          case 'user':
            return new HumanMessage(msg.content);
          case 'assistant':
            if (msg.toolCalls) {
              // 处理带有工具调用的助手消息
              return new AIMessage({
                content: msg.content,
                tool_calls: msg.toolCalls.map(tc => ({
                  id: tc.id,
                  name: tc.name,
                  args: tc.arguments
                }))
              });
            }
            return new AIMessage(msg.content);
          case 'system':
            return new SystemMessage(msg.content);
          case 'tool':
            return new ToolMessage({
              content: msg.content,
              tool_call_id: msg.toolCallId || ''
            });
          default:
            return new HumanMessage(msg.content);
        }
      });

      // 准备工具定义
      const toolDefinitions = tools?.map(tool => ({
        type: "function" as const,
        function: {
          name: tool.name,
          description: tool.description,
          parameters: tool.parameters
        }
      }));

      const response = await this.client.invoke(langChainMessages, {
        tools: toolDefinitions || undefined
      });

      // 解析响应
      const content = response.content as string;
      const toolCalls: ToolCall[] = [];

      // 检查是否有工具调用
      if (response.tool_calls && response.tool_calls.length > 0) {
        for (const toolCall of response.tool_calls) {
          toolCalls.push({
            id: toolCall.id || `call_${Date.now()}`,
            name: toolCall.name,
            arguments: toolCall.args || {}
          });
        }
      }

      return {
        content,
        toolCalls: toolCalls.length > 0 ? toolCalls : undefined
      };
    } catch (error) {
      console.error('DeepSeek API 调用失败:', error);
      throw new Error(`DeepSeek API 错误: ${error instanceof Error ? error.message : '未知错误'}`);
    }
  }
}