/**
 * MCP (Model Context Protocol) 相关类型定义
 */

// 基础 MCP 类型定义（临时替代，避免复杂的导入问题）
export interface Tool {
  name: string;
  description?: string;
  inputSchema?: any;
}

export interface Resource {
  uri: string;
  name: string;
  description?: string;
  mimeType?: string;
}

export interface Prompt {
  name: string;
  description?: string;
  arguments?: any[];
}

export interface ServerCapabilities {
  experimental?: Record<string, any>;
  logging?: Record<string, any>;
  prompts?: {
    listChanged?: boolean;
  };
  resources?: {
    subscribe?: boolean;
    listChanged?: boolean;
  };
  tools?: {
    listChanged?: boolean;
  };
}

export interface CallToolRequest {
  method: 'tools/call';
  params: {
    name: string;
    arguments?: Record<string, any>;
  };
}

export interface CallToolResult {
  content: Array<{
    type: 'text' | 'image' | 'resource';
    text?: string;
    data?: string;
    mimeType?: string;
  }>;
  isError?: boolean;
}

export interface ListToolsRequest {
  method: 'tools/list';
  params?: {
    cursor?: string;
  };
}

export interface ListToolsResult {
  tools: Tool[];
  nextCursor?: string;
}

export interface ListResourcesRequest {
  method: 'resources/list';
  params?: {
    cursor?: string;
  };
}

export interface ListResourcesResult {
  resources: Resource[];
  nextCursor?: string;
}

export interface ListPromptsRequest {
  method: 'prompts/list';
  params?: {
    cursor?: string;
  };
}

export interface ListPromptsResult {
  prompts: Prompt[];
  nextCursor?: string;
}

export interface ReadResourceRequest {
  method: 'resources/read';
  params: {
    uri: string;
  };
}

export interface ReadResourceResult {
  contents: Array<{
    uri: string;
    mimeType?: string;
    text?: string;
    blob?: string;
  }>;
}

export interface GetPromptRequest {
  method: 'prompts/get';
  params: {
    name: string;
    arguments?: Record<string, any>;
  };
}

export interface GetPromptResult {
  description?: string;
  messages: Array<{
    role: 'user' | 'assistant';
    content: {
      type: 'text' | 'image' | 'resource';
      text?: string;
      data?: string;
      mimeType?: string;
    };
  }>;
}

export interface InitializeRequest {
  method: 'initialize';
  params: {
    protocolVersion: string;
    capabilities: ClientCapabilities;
    clientInfo: {
      name: string;
      version: string;
    };
  };
}

export interface InitializeResult {
  protocolVersion: string;
  capabilities: ServerCapabilities;
  serverInfo: {
    name: string;
    version: string;
  };
}

export interface ClientCapabilities {
  experimental?: Record<string, any>;
  logging?: Record<string, any>;
  prompts?: {
    listChanged?: boolean;
  };
  resources?: {
    subscribe?: boolean;
    listChanged?: boolean;
  };
  tools?: {
    listChanged?: boolean;
  };
}

export interface Implementation {
  name: string;
  version: string;
}

export interface JSONRPCMessage {
  jsonrpc: '2.0';
}

export interface JSONRPCRequest extends JSONRPCMessage {
  id: string | number;
  method: string;
  params?: any;
}

export interface JSONRPCResponse extends JSONRPCMessage {
  id: string | number;
  result?: any;
  error?: JSONRPCError;
}

export interface JSONRPCError {
  code: number;
  message: string;
  data?: any;
}

/**
 * MCP 服务器连接配置
 */
export interface MCPServerConfig {
  /** 服务器唯一标识符 */
  id: string;
  /** 服务器显示名称 */
  name: string;
  /** 服务器描述 */
  description?: string;
  /** 传输类型 */
  transport: MCPTransportType;
  /** 连接参数 */
  connection: MCPConnectionConfig;
  /** 是否启用 */
  enabled: boolean;
  /** 超时设置（毫秒） */
  timeout?: number;
  /** 重试配置 */
  retry?: MCPRetryConfig;
  /** 环境变量 */
  env?: Record<string, string>;
}

/**
 * MCP 传输类型
 */
export type MCPTransportType = 'stdio' | 'sse' | 'websocket';

/**
 * MCP 连接配置
 */
export type MCPConnectionConfig = 
  | MCPStdioConfig 
  | MCPSSEConfig 
  | MCPWebSocketConfig;

/**
 * Stdio 传输配置
 */
export interface MCPStdioConfig {
  type: 'stdio';
  /** 可执行文件路径 */
  command: string;
  /** 命令行参数 */
  args?: string[];
  /** 工作目录 */
  cwd?: string;
}

/**
 * SSE 传输配置
 */
export interface MCPSSEConfig {
  type: 'sse';
  /** 服务器 URL */
  url: string;
  /** 请求头 */
  headers?: Record<string, string>;
}

/**
 * WebSocket 传输配置
 */
export interface MCPWebSocketConfig {
  type: 'websocket';
  /** WebSocket URL */
  url: string;
  /** 协议 */
  protocols?: string[];
  /** 请求头 */
  headers?: Record<string, string>;
}

/**
 * MCP 重试配置
 */
export interface MCPRetryConfig {
  /** 最大重试次数 */
  maxRetries: number;
  /** 初始延迟（毫秒） */
  initialDelay: number;
  /** 最大延迟（毫秒） */
  maxDelay: number;
  /** 退避倍数 */
  backoffMultiplier: number;
}

/**
 * MCP 客户端状态
 */
export type MCPClientStatus = 
  | 'disconnected' 
  | 'connecting' 
  | 'connected' 
  | 'error' 
  | 'reconnecting';

/**
 * MCP 客户端信息
 */
export interface MCPClientInfo {
  /** 服务器配置 */
  config: MCPServerConfig;
  /** 连接状态 */
  status: MCPClientStatus;
  /** 服务器能力 */
  capabilities?: ServerCapabilities;
  /** 可用工具列表 */
  tools: Tool[];
  /** 可用资源列表 */
  resources: Resource[];
  /** 可用提示列表 */
  prompts: Prompt[];
  /** 最后连接时间 */
  lastConnected?: Date;
  /** 错误信息 */
  error?: string;
  /** 连接统计 */
  stats: MCPConnectionStats;
}

/**
 * MCP 连接统计
 */
export interface MCPConnectionStats {
  /** 连接次数 */
  connectCount: number;
  /** 重连次数 */
  reconnectCount: number;
  /** 工具调用次数 */
  toolCallCount: number;
  /** 资源读取次数 */
  resourceReadCount: number;
  /** 提示获取次数 */
  promptGetCount: number;
  /** 总错误次数 */
  errorCount: number;
}

/**
 * MCP 工具调用上下文
 */
export interface MCPToolContext {
  /** 服务器 ID */
  serverId: string;
  /** 工具名称 */
  toolName: string;
  /** 调用参数 */
  arguments: Record<string, any>;
  /** 调用时间戳 */
  timestamp: Date;
  /** 用户 ID（如果有） */
  userId?: string;
}

/**
 * MCP 事件类型
 */
export type MCPEventType = 
  | 'client_connected'
  | 'client_disconnected'
  | 'client_error'
  | 'tool_called'
  | 'resource_read'
  | 'prompt_get'
  | 'capabilities_updated';

/**
 * MCP 事件数据
 */
export interface MCPEvent {
  /** 事件类型 */
  type: MCPEventType;
  /** 服务器 ID */
  serverId: string;
  /** 事件时间戳 */
  timestamp: Date;
  /** 事件数据 */
  data?: any;
  /** 错误信息（如果有） */
  error?: string;
}

/**
 * MCP 管理器配置
 */
export interface MCPManagerConfig {
  /** 默认超时时间（毫秒） */
  defaultTimeout: number;
  /** 默认重试配置 */
  defaultRetry: MCPRetryConfig;
  /** 最大并发连接数 */
  maxConcurrentConnections: number;
  /** 是否启用自动重连 */
  autoReconnect: boolean;
  /** 健康检查间隔（毫秒） */
  healthCheckInterval: number;
}

/**
 * MCP 工具包装器，用于集成到现有的工具系统
 */
export interface MCPToolWrapper {
  /** 原始 MCP 工具 */
  tool: Tool;
  /** 服务器 ID */
  serverId: string;
  /** 服务器名称 */
  serverName: string;
  /** 调用函数 */
  call: (args: Record<string, any>) => Promise<CallToolResult>;
}
