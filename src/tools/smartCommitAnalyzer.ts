import { execSync } from 'child_process';

export interface ChangeAnalysis {
  primaryType: string;
  scope: string | null;
  description: string;
  files: string[];
  additions: number;
  deletions: number;
  isBreakingChange: boolean;
  confidence: number; // 分析置信度 0-1
  reasoning: string; // 分析推理过程
}

export class SmartCommitAnalyzer {
  
  /**
   * 深度分析 git diff 内容，生成智能的 commit 信息
   */
  public static analyzeDiff(diffContent: string): ChangeAnalysis {
    const analyzer = new SmartCommitAnalyzer();
    return analyzer.performAnalysis(diffContent);
  }

  private performAnalysis(diffContent: string): ChangeAnalysis {
    const baseAnalysis = this.extractBasicInfo(diffContent);
    const codeAnalysis = this.analyzeCodeChanges(diffContent);
    const semanticAnalysis = this.analyzeSemanticMeaning(diffContent, baseAnalysis);
    const contextAnalysis = this.analyzeProjectContext(baseAnalysis.files);
    
    // 综合分析结果
    return this.synthesizeAnalysis(baseAnalysis, codeAnalysis, semanticAnalysis, contextAnalysis);
  }

  /**
   * 提取基础信息：文件、行数等
   */
  private extractBasicInfo(diffContent: string) {
    const lines = diffContent.split('\n');
    const files: string[] = [];
    let additions = 0;
    let deletions = 0;
    let isBreakingChange = false;

    for (const line of lines) {
      if (line.startsWith('diff --git')) {
        const match = line.match(/diff --git a\/(.*) b\/(.*)/);
        if (match) files.push(match[1]);
      } else if (line.startsWith('+') && !line.startsWith('+++')) {
        additions++;
      } else if (line.startsWith('-') && !line.startsWith('---')) {
        deletions++;
      } else if (line.includes('BREAKING CHANGE') || line.includes('breaking')) {
        isBreakingChange = true;
      }
    }

    return { files, additions, deletions, isBreakingChange };
  }

  /**
   * 分析代码变更的语义内容
   */
  private analyzeCodeChanges(diffContent: string) {
    const patterns = {
      // 功能相关
      newFunction: /^\+.*(?:function|const.*=|class|interface|type)/gm,
      newMethod: /^\+.*(?:public|private|protected).*\(/gm,
      newImport: /^\+.*import.*from/gm,
      newExport: /^\+.*export/gm,
      
      // 修复相关
      bugKeywords: /^\+.*(?:fix|bug|error|issue|problem|解决|修复|修正)/gim,
      errorHandling: /^\+.*(?:try|catch|throw|Error)/gm,
      validation: /^\+.*(?:validate|check|verify|校验|验证)/gm,
      
      // 重构相关
      renaming: /^\-.*(?:function|const|class|interface).*\n\+.*(?:function|const|class|interface)/gm,
      restructure: /^\-.*\n\+.*$/gm,
      
      // 测试相关
      testCode: /^\+.*(?:test|spec|describe|it|expect|assert|测试)/gim,
      
      // 文档相关
      comments: /^\+.*(?:\/\/|\/\*|\*|#|<!--)/gm,
      readme: /README|CHANGELOG|\.md$/i,
      
      // 配置相关
      config: /^\+.*(?:config|setting|option|\.json|\.yml|\.yaml|\.env)/gim,
      
      // 性能优化
      performance: /^\+.*(?:optimize|performance|cache|async|await|性能|优化)/gim,
      
      // 安全相关
      security: /^\+.*(?:auth|secure|permission|encrypt|decrypt|安全|权限|加密)/gim,
      
      // UI/样式相关
      styling: /^\+.*(?:style|css|scss|theme|color|ui|界面|样式)/gim,
      
      // API相关
      apiChanges: /^\+.*(?:api|endpoint|route|request|response|接口)/gim,
    };

    const analysis = {
      hasNewFunctions: patterns.newFunction.test(diffContent),
      hasNewMethods: patterns.newMethod.test(diffContent),
      hasNewImports: patterns.newImport.test(diffContent),
      hasNewExports: patterns.newExport.test(diffContent),
      hasBugFixes: patterns.bugKeywords.test(diffContent),
      hasErrorHandling: patterns.errorHandling.test(diffContent),
      hasValidation: patterns.validation.test(diffContent),
      hasRenaming: patterns.renaming.test(diffContent),
      hasRestructuring: patterns.restructure.test(diffContent),
      hasTestCode: patterns.testCode.test(diffContent),
      hasComments: patterns.comments.test(diffContent),
      hasConfig: patterns.config.test(diffContent),
      hasPerformance: patterns.performance.test(diffContent),
      hasSecurity: patterns.security.test(diffContent),
      hasStyling: patterns.styling.test(diffContent),
      hasApiChanges: patterns.apiChanges.test(diffContent),
    };

    return analysis;
  }

  /**
   * 分析语义含义，理解变更的真实意图
   */
  private analyzeSemanticMeaning(diffContent: string, basicInfo: any) {
    const addedLines = diffContent.split('\n').filter(line => line.startsWith('+')).join('\n');
    const removedLines = diffContent.split('\n').filter(line => line.startsWith('-')).join('\n');
    
    // 分析新增内容的关键词
    const newContent = addedLines.toLowerCase();
    const oldContent = removedLines.toLowerCase();
    
    return {
      isNewFeature: this.detectNewFeature(newContent, basicInfo),
      isBugFix: this.detectBugFix(newContent, oldContent),
      isRefactor: this.detectRefactor(addedLines, removedLines, basicInfo),
      isDocumentation: this.detectDocumentation(basicInfo.files, newContent),
      isTest: this.detectTest(basicInfo.files, newContent),
      isConfig: this.detectConfiguration(basicInfo.files, newContent),
      isPerformance: this.detectPerformance(newContent),
      isSecurity: this.detectSecurity(newContent),
    };
  }

  /**
   * 分析项目上下文，理解文件在项目中的作用
   */
  private analyzeProjectContext(files: string[]) {
    const context = {
      scope: this.determineSmartScope(files),
      component: this.identifyComponent(files),
      layer: this.identifyArchitectureLayer(files),
    };

    return context;
  }

  /**
   * 综合所有分析结果，生成最终的 commit 信息
   */
  private synthesizeAnalysis(
    basicInfo: any, 
    codeAnalysis: any, 
    semanticAnalysis: any, 
    contextAnalysis: any
  ): ChangeAnalysis {
    
    // 确定变更类型（优先级排序）
    const typeAnalysis = this.determineChangeType(codeAnalysis, semanticAnalysis, basicInfo);
    
    // 生成描述
    const description = this.generateSmartDescription(
      typeAnalysis.type, 
      codeAnalysis, 
      semanticAnalysis, 
      contextAnalysis, 
      basicInfo
    );

    return {
      primaryType: typeAnalysis.type,
      scope: contextAnalysis.scope,
      description,
      files: basicInfo.files,
      additions: basicInfo.additions,
      deletions: basicInfo.deletions,
      isBreakingChange: basicInfo.isBreakingChange,
      confidence: typeAnalysis.confidence,
      reasoning: typeAnalysis.reasoning + ' ' + description
    };
  }

  /**
   * 智能确定变更类型
   */
  private determineChangeType(codeAnalysis: any, semanticAnalysis: any, basicInfo: any) {
    const scores = {
      feat: 0,
      fix: 0,
      refactor: 0,
      docs: 0,
      test: 0,
      chore: 0,
      perf: 0,
      security: 0
    };

    // 特征评分
    if (semanticAnalysis.isBugFix || codeAnalysis.hasBugFixes) scores.fix += 3;
    if (semanticAnalysis.isNewFeature || codeAnalysis.hasNewFunctions) scores.feat += 3;
    if (semanticAnalysis.isRefactor || codeAnalysis.hasRenaming) scores.refactor += 3;
    if (semanticAnalysis.isDocumentation) scores.docs += 3;
    if (semanticAnalysis.isTest) scores.test += 3;
    if (semanticAnalysis.isConfig) scores.chore += 2;
    if (semanticAnalysis.isPerformance) scores.perf += 3;
    if (semanticAnalysis.isSecurity) scores.security += 3;

    // 次要特征
    if (codeAnalysis.hasNewImports) scores.feat += 1;
    if (codeAnalysis.hasErrorHandling) scores.fix += 1;
    if (codeAnalysis.hasNewMethods) scores.feat += 1;
    if (codeAnalysis.hasRestructuring && basicInfo.deletions > basicInfo.additions) {
      scores.refactor += 2;
    }

    // 找到最高分
    const topType = Object.entries(scores).reduce((a, b) => 
      (scores as any)[a[0]] > (scores as any)[b[0]] ? a : b
    );
    const confidence = Math.min(topType[1] / 5, 1); // 最高置信度为1

    return {
      type: topType[0],
      confidence,
      reasoning: `基于代码分析评分: ${JSON.stringify(scores)}`
    };
  }

  /**
   * 生成智能描述
   */
  private generateSmartDescription(
    type: string, 
    codeAnalysis: any, 
    semanticAnalysis: any, 
    contextAnalysis: any, 
    basicInfo: any
  ): string {
    const component = contextAnalysis.component;
    const hasMultipleFiles = basicInfo.files.length > 1;

    switch (type) {
      case 'feat':
        if (codeAnalysis.hasNewFunctions) {
          return `添加${component}功能模块`;
        }
        if (codeAnalysis.hasNewMethods) {
          return `增强${component}能力`;
        }
        if (codeAnalysis.hasApiChanges) {
          return `新增${component} API 接口`;
        }
        return `实现${component}新功能`;

      case 'fix':
        if (codeAnalysis.hasErrorHandling) {
          return `修复${component}错误处理机制`;
        }
        if (codeAnalysis.hasValidation) {
          return `修复${component}数据验证问题`;
        }
        return `修复${component}相关问题`;

      case 'refactor':
        if (codeAnalysis.hasRenaming) {
          return `重构${component}命名规范`;
        }
        if (basicInfo.deletions > basicInfo.additions * 1.5) {
          return `简化${component}实现逻辑`;
        }
        return `重构${component}代码结构`;

      case 'perf':
        return `优化${component}性能表现`;

      case 'security':
        return `加强${component}安全机制`;

      case 'docs':
        return hasMultipleFiles ? '更新项目文档' : `完善${component}文档`;

      case 'test':
        return `添加${component}测试用例`;

      case 'chore':
        if (codeAnalysis.hasConfig) {
          return `调整${component}配置`;
        }
        return `维护${component}相关配置`;

      default:
        return `更新${component}模块`;
    }
  }

  // === 辅助检测方法 ===

  private detectNewFeature(content: string, basicInfo: any): boolean {
    const newFeatureIndicators = [
      'new ', 'add', 'implement', 'create', 'introduce',
      '新增', '添加', '实现', '创建', '引入'
    ];
    return newFeatureIndicators.some(indicator => content.includes(indicator)) ||
           basicInfo.files.some((f: string) => f.includes('new') || !f.includes('.'));
  }

  private detectBugFix(newContent: string, oldContent: string): boolean {
    const bugIndicators = ['fix', 'bug', 'error', 'issue', 'problem', '修复', '修正', '解决'];
    return bugIndicators.some(indicator => 
      newContent.includes(indicator) || oldContent.includes(indicator)
    );
  }

  private detectRefactor(addedLines: string, removedLines: string, basicInfo: any): boolean {
    // 如果删除的比新增的多，且没有新文件，可能是重构
    return basicInfo.deletions > basicInfo.additions * 0.8 && 
           !basicInfo.files.some((f: string) => f.includes('new'));
  }

  private detectDocumentation(files: string[], content: string): boolean {
    const docFiles = files.some(f => /\.(md|txt|rst)$/i.test(f) || /readme|changelog|doc/i.test(f));
    const docContent = /comment|doc|readme|说明|文档/.test(content);
    return docFiles || docContent;
  }

  private detectTest(files: string[], content: string): boolean {
    const testFiles = files.some(f => /\.(test|spec)\.|test\/|spec\//i.test(f));
    const testContent = /test|spec|describe|it\(|expect|assert|测试/.test(content);
    return testFiles || testContent;
  }

  private detectConfiguration(files: string[], content: string): boolean {
    const configFiles = files.some(f => /\.(json|yml|yaml|env|config)$/i.test(f));
    const configContent = /config|setting|option|配置/.test(content);
    return configFiles || configContent;
  }

  private detectPerformance(content: string): boolean {
    return /optimize|performance|cache|async|await|性能|优化/.test(content);
  }

  private detectSecurity(content: string): boolean {
    return /auth|secure|permission|encrypt|decrypt|安全|权限|加密/.test(content);
  }

  /**
   * 智能确定 scope
   */
  private determineSmartScope(files: string[]): string | null {
    if (files.length === 0) return null;
    
    // 按文件夹分组
    const folders = new Map<string, number>();
    files.forEach(file => {
      const parts = file.split('/');
      if (parts.length > 1) {
        const folder = parts[0];
        folders.set(folder, (folders.get(folder) || 0) + 1);
      }
    });

    // 找到最常见的文件夹
    if (folders.size > 0) {
      const topFolder = Array.from(folders.entries())
        .sort((a, b) => b[1] - a[1])[0][0];
      
      // 特殊处理一些常见的文件夹
      const scopeMapping: { [key: string]: string } = {
        'src': this.getSubScope(files) || 'core',
        'lib': 'core',
        'utils': 'utils',
        'components': 'ui',
        'pages': 'ui',
        'styles': 'style',
        'docs': 'docs',
        'test': 'test',
        'tests': 'test',
        'config': 'config',
        'tools': 'tools',
        'scripts': 'scripts'
      };

      return scopeMapping[topFolder] || topFolder;
    }

    return null;
  }

  private getSubScope(files: string[]): string | null {
    const srcFiles = files.filter(f => f.startsWith('src/'));
    if (srcFiles.length === 0) return null;

    const subFolders = new Map<string, number>();
    srcFiles.forEach(file => {
      const parts = file.split('/');
      if (parts.length > 2) {
        const subFolder = parts[1];
        subFolders.set(subFolder, (subFolders.get(subFolder) || 0) + 1);
      }
    });

    if (subFolders.size > 0) {
      return Array.from(subFolders.entries())
        .sort((a, b) => b[1] - a[1])[0][0];
    }

    return null;
  }

  /**
   * 识别组件类型
   */
  private identifyComponent(files: string[]): string {
    if (files.length === 0) return '模块';

    // 基于文件名和路径识别组件类型
    for (const file of files) {
      if (file.includes('tool')) return '工具';
      if (file.includes('manager')) return '管理器';
      if (file.includes('provider')) return '提供商';
      if (file.includes('service')) return '服务';
      if (file.includes('component')) return '组件';
      if (file.includes('util')) return '工具';
      if (file.includes('config')) return '配置';
      if (file.includes('test')) return '测试';
      if (file.includes('doc')) return '文档';
      if (file.includes('api')) return 'API';
      if (file.includes('cli')) return 'CLI';
      if (file.includes('core')) return '核心';
    }

    return '模块';
  }

  /**
   * 识别架构层次
   */
  private identifyArchitectureLayer(files: string[]): string {
    const layers = ['ui', 'service', 'core', 'data', 'config'];
    // 可以根据文件路径进一步分析架构层次
    return 'application';
  }
}