import { Tool } from '../types';
import { existsSync, readFileSync, writeFileSync, mkdirSync, statSync, readdirSync } from 'fs';
import { dirname, join, extname, basename } from 'path';
import chalk from 'chalk';

/**
 * 文件读取工具
 */
export const fileReadTool: Tool = {
  name: 'read_file',
  description: '读取文件内容',
  parameters: {
    type: 'object',
    properties: {
      filePath: {
        type: 'string',
        description: '要读取的文件路径（相对或绝对路径）'
      },
      encoding: {
        type: 'string',
        description: '文件编码，默认为 utf8',
        enum: ['utf8', 'ascii', 'base64', 'binary']
      }
    },
    required: ['filePath']
  },
  execute: async (args: Record<string, any>) => {
    const { filePath, encoding = 'utf8' } = args;
    
    try {
      const fullPath = join(process.cwd(), filePath);
      
      if (!existsSync(fullPath)) {
        return {
          success: false,
          error: `文件不存在: ${filePath}`
        };
      }

      const stats = statSync(fullPath);
      if (stats.isDirectory()) {
        return {
          success: false,
          error: `路径是目录，不是文件: ${filePath}`
        };
      }

      const content = readFileSync(fullPath, encoding);
      const fileInfo = {
        path: filePath,
        size: stats.size,
        modified: stats.mtime,
        extension: extname(filePath),
        lines: encoding === 'utf8' ? String(content).split('\n').length : null
      };

      return {
        success: true,
        content,
        fileInfo,
        message: `成功读取文件: ${filePath} (${(stats.size / 1024).toFixed(2)} KB)`
      };
    } catch (error) {
      return {
        success: false,
        error: `读取文件失败: ${error instanceof Error ? error.message : '未知错误'}`
      };
    }
  }
};

/**
 * 文件写入工具
 */
export const fileWriteTool: Tool = {
  name: 'write_file',
  description: '写入文件内容',
  parameters: {
    type: 'object',
    properties: {
      filePath: {
        type: 'string',
        description: '要写入的文件路径'
      },
      content: {
        type: 'string',
        description: '要写入的内容'
      },
      mode: {
        type: 'string',
        description: '写入模式',
        enum: ['overwrite', 'append']
      },
      createDirs: {
        type: 'boolean',
        description: '是否自动创建目录，默认为 true'
      },
      backup: {
        type: 'boolean',
        description: '是否创建备份文件，默认为 false'
      }
    },
    required: ['filePath', 'content']
  },
  execute: async (args: Record<string, any>) => {
    const { filePath, content, mode = 'overwrite', createDirs = true, backup = false } = args;
    
    try {
      const fullPath = join(process.cwd(), filePath);
      const dir = dirname(fullPath);
      
      // 检查并创建目录
      if (createDirs && !existsSync(dir)) {
        mkdirSync(dir, { recursive: true });
      }

      // 创建备份
      if (backup && existsSync(fullPath)) {
        const backupPath = `${fullPath}.backup.${Date.now()}`;
        const originalContent = readFileSync(fullPath, 'utf8');
        writeFileSync(backupPath, originalContent);
      }

      // 写入文件
      let finalContent = content;
      if (mode === 'append' && existsSync(fullPath)) {
        const existingContent = readFileSync(fullPath, 'utf8');
        finalContent = existingContent + '\n' + content;
      }

      writeFileSync(fullPath, finalContent, 'utf8');
      
      const stats = statSync(fullPath);
      const lines = finalContent.split('\n').length;

      return {
        success: true,
        filePath,
        size: stats.size,
        lines,
        mode,
        message: `成功${mode === 'append' ? '追加' : '写入'}文件: ${filePath} (${lines} 行, ${(stats.size / 1024).toFixed(2)} KB)`
      };
    } catch (error) {
      return {
        success: false,
        error: `写入文件失败: ${error instanceof Error ? error.message : '未知错误'}`
      };
    }
  }
};

/**
 * Markdown 文件分析工具
 */
export const markdownAnalyzeTool: Tool = {
  name: 'analyze_markdown',
  description: '分析 Markdown 文件结构和内容',
  parameters: {
    type: 'object',
    properties: {
      filePath: {
        type: 'string',
        description: '要分析的 Markdown 文件路径'
      }
    },
    required: ['filePath']
  },
  execute: async (args: Record<string, any>) => {
    const { filePath } = args;
    
    try {
      const fullPath = join(process.cwd(), filePath);
      
      if (!existsSync(fullPath)) {
        return {
          success: false,
          error: `文件不存在: ${filePath}`
        };
      }

      const content = readFileSync(fullPath, 'utf8');
      const lines = content.split('\n');
      
      // 分析 Markdown 结构
      const analysis = {
        totalLines: lines.length,
        headings: [] as Array<{level: number, text: string, line: number}>,
        codeBlocks: [] as Array<{language: string, lines: number, start: number}>,
        links: [] as Array<{text: string, url: string, line: number}>,
        images: [] as Array<{alt: string, url: string, line: number}>,
        tables: 0,
        wordCount: 0
      };

      let inCodeBlock = false;
      let codeBlockStart = 0;
      let codeBlockLang = '';

      lines.forEach((line, index) => {
        const lineNum = index + 1;
        
        // 标题
        const headingMatch = line.match(/^(#{1,6})\s+(.+)$/);
        if (headingMatch && !inCodeBlock) {
          analysis.headings.push({
            level: headingMatch[1].length,
            text: headingMatch[2],
            line: lineNum
          });
        }

        // 代码块
        const codeBlockMatch = line.match(/^```(\w*)/);
        if (codeBlockMatch) {
          if (!inCodeBlock) {
            inCodeBlock = true;
            codeBlockStart = lineNum;
            codeBlockLang = codeBlockMatch[1] || 'text';
          } else {
            inCodeBlock = false;
            analysis.codeBlocks.push({
              language: codeBlockLang,
              lines: lineNum - codeBlockStart - 1,
              start: codeBlockStart
            });
          }
        }

        // 链接
        const linkMatches = line.matchAll(/\[([^\]]+)\]\(([^)]+)\)/g);
        for (const match of linkMatches) {
          analysis.links.push({
            text: match[1],
            url: match[2],
            line: lineNum
          });
        }

        // 图片
        const imageMatches = line.matchAll(/!\[([^\]]*)\]\(([^)]+)\)/g);
        for (const match of imageMatches) {
          analysis.images.push({
            alt: match[1],
            url: match[2],
            line: lineNum
          });
        }

        // 表格
        if (line.includes('|') && !inCodeBlock) {
          analysis.tables++;
        }

        // 字数统计（简单统计）
        if (!inCodeBlock) {
          analysis.wordCount += line.split(/\s+/).filter(word => word.length > 0).length;
        }
      });

      return {
        success: true,
        filePath,
        analysis,
        message: `Markdown 分析完成: ${analysis.headings.length} 个标题, ${analysis.codeBlocks.length} 个代码块, ${analysis.wordCount} 个词`
      };
    } catch (error) {
      return {
        success: false,
        error: `分析 Markdown 文件失败: ${error instanceof Error ? error.message : '未知错误'}`
      };
    }
  }
};

/**
 * 文件列表工具
 */
export const fileListTool: Tool = {
  name: 'list_files',
  description: '列出目录中的文件',
  parameters: {
    type: 'object',
    properties: {
      dirPath: {
        type: 'string',
        description: '要列出的目录路径，默认为当前目录'
      },
      pattern: {
        type: 'string',
        description: '文件名模式（如 *.md, *.js）'
      },
      recursive: {
        type: 'boolean',
        description: '是否递归列出子目录'
      }
    },
    required: []
  },
  execute: async (args: Record<string, any>) => {
    const { dirPath = '.', pattern, recursive = false } = args;
    
    try {
      const fullPath = join(process.cwd(), dirPath);
      
      if (!existsSync(fullPath)) {
        return {
          success: false,
          error: `目录不存在: ${dirPath}`
        };
      }

      const stats = statSync(fullPath);
      if (!stats.isDirectory()) {
        return {
          success: false,
          error: `路径不是目录: ${dirPath}`
        };
      }

      function listFilesRecursive(dir: string, currentDepth = 0): Array<{name: string, path: string, size: number, isDir: boolean, modified: Date}> {
        const items: Array<{name: string, path: string, size: number, isDir: boolean, modified: Date}> = [];
        
        try {
          const entries = readdirSync(dir);
          
          for (const entry of entries) {
            const entryPath = join(dir, entry);
            const entryStats = statSync(entryPath);
            const relativePath = entryPath.replace(process.cwd() + '/', '');
            
            // 应用模式过滤
            if (pattern && !entryStats.isDirectory()) {
              const regex = new RegExp(pattern.replace(/\*/g, '.*'));
              if (!regex.test(entry)) {
                continue;
              }
            }
            
            items.push({
              name: entry,
              path: relativePath,
              size: entryStats.size,
              isDir: entryStats.isDirectory(),
              modified: entryStats.mtime
            });
            
            // 递归处理子目录
            if (recursive && entryStats.isDirectory() && currentDepth < 3) {
              items.push(...listFilesRecursive(entryPath, currentDepth + 1));
            }
          }
        } catch (error) {
          // 跳过无法访问的目录
        }
        
        return items;
      }

      const files = listFilesRecursive(fullPath);
      
      return {
        success: true,
        dirPath,
        files,
        totalFiles: files.filter(f => !f.isDir).length,
        totalDirs: files.filter(f => f.isDir).length,
        message: `找到 ${files.length} 个项目 (${files.filter(f => !f.isDir).length} 个文件, ${files.filter(f => f.isDir).length} 个目录)`
      };
    } catch (error) {
      return {
        success: false,
        error: `列出文件失败: ${error instanceof Error ? error.message : '未知错误'}`
      };
    }
  }
};

/**
 * 智能文件操作工具 - 与 LLM 交互
 */
export const smartFileTool: Tool = {
  name: 'smart_file_operation',
  description: '智能文件操作：根据用户意图执行文件操作，可以读取文件让 AI 分析，或让 AI 生成内容写入文件',
  parameters: {
    type: 'object',
    properties: {
      operation: {
        type: 'string',
        description: '操作类型',
        enum: ['read_for_analysis', 'write_ai_content', 'edit_with_ai', 'analyze_structure', 'list_and_suggest']
      },
      filePath: {
        type: 'string',
        description: '文件路径'
      },
      userRequest: {
        type: 'string',
        description: '用户的具体需求，例如："帮我分析这个文件"、"根据这个内容生成一个新的 README"'
      },
      content: {
        type: 'string',
        description: 'AI 生成的内容（用于写入文件）'
      },
      editInstructions: {
        type: 'string',
        description: '编辑指令，告诉用户如何修改文件'
      }
    },
    required: ['operation', 'userRequest']
  },
  execute: async (args: Record<string, any>) => {
    const { operation, filePath, userRequest, content, editInstructions } = args;
    
    try {
      switch (operation) {
        case 'read_for_analysis':
          if (!filePath) {
            return { 
              success: false, 
              error: '需要指定文件路径',
              suggestion: '请提供要分析的文件路径，例如：README.md' 
            };
          }
          
          const readResult = await fileReadTool.execute({ filePath });
          if (!readResult.success) return readResult;
          
          return {
            success: true,
            operation: 'read_for_analysis',
            filePath,
            content: readResult.content,
            fileInfo: readResult.fileInfo,
            message: `已读取文件 ${filePath}，内容已提供给 AI 进行分析`,
            aiContext: `用户请求：${userRequest}\n\n文件内容：\n${readResult.content}`
          };
          
        case 'write_ai_content':
          if (!filePath || !content) {
            return { 
              success: false, 
              error: '需要指定文件路径和内容' 
            };
          }
          
          const writeResult = await fileWriteTool.execute({ 
            filePath, 
            content, 
            createDirs: true,
            backup: existsSync(join(process.cwd(), filePath))
          });
          
          return {
            success: writeResult.success,
            operation: 'write_ai_content',
            filePath,
            message: writeResult.success 
              ? `AI 生成的内容已写入文件 ${filePath}` 
              : writeResult.error,
            details: writeResult
          };
          
        case 'edit_with_ai':
          if (!filePath) {
            return { 
              success: false, 
              error: '需要指定要编辑的文件路径' 
            };
          }
          
          const originalFile = await fileReadTool.execute({ filePath });
          if (!originalFile.success) {
            return {
              success: false,
              error: `无法读取文件 ${filePath}: ${originalFile.error}`,
              suggestion: '请确认文件路径是否正确'
            };
          }
          
          return {
            success: true,
            operation: 'edit_with_ai',
            filePath,
            originalContent: originalFile.content,
            fileInfo: originalFile.fileInfo,
            editInstructions: editInstructions || '请根据用户需求修改此文件',
            message: `已读取文件 ${filePath}，请提供修改后的内容`,
            aiContext: `用户编辑请求：${userRequest}\n\n当前文件内容：\n${originalFile.content}\n\n请根据用户需求提供修改后的完整文件内容。`
          };
          
        case 'analyze_structure':
          if (!filePath) {
            return { 
              success: false, 
              error: '需要指定要分析的文件路径' 
            };
          }
          
          const ext = extname(filePath).toLowerCase();
          let analysisResult;
          
          if (ext === '.md' || ext === '.markdown') {
            analysisResult = await markdownAnalyzeTool.execute({ filePath });
          } else {
            const fileResult = await fileReadTool.execute({ filePath });
            if (!fileResult.success) return fileResult;
            
            analysisResult = {
              success: true,
              analysis: {
                extension: ext,
                lines: fileResult.content.split('\n').length,
                size: fileResult.fileInfo.size,
                wordCount: fileResult.content.split(/\s+/).length
              },
              content: fileResult.content
            };
          }
          
          return {
            success: analysisResult.success,
            operation: 'analyze_structure',
            filePath,
            analysis: analysisResult.analysis,
            message: `文件结构分析完成：${filePath}`,
            aiContext: `用户分析请求：${userRequest}\n\n分析结果：${JSON.stringify(analysisResult.analysis, null, 2)}${analysisResult.content ? `\n\n文件内容：\n${analysisResult.content}` : ''}`
          };
          
        case 'list_and_suggest':
          const listResult = await fileListTool.execute({ 
            dirPath: filePath || '.', 
            pattern: '*.md' // 默认关注 markdown 文件
          });
          
          if (!listResult.success) return listResult;
          
          return {
            success: true,
            operation: 'list_and_suggest',
            files: listResult.files,
            message: `找到 ${listResult.totalFiles} 个文件`,
            aiContext: `用户请求：${userRequest}\n\n文件列表：\n${listResult.files.map((f: any) => `- ${f.path} (${f.isDir ? '目录' : '文件'}, ${(f.size/1024).toFixed(2)}KB)`).join('\n')}`
          };
          
        default:
          return {
            success: false,
            error: `不支持的操作类型: ${operation}`,
            availableOperations: ['read_for_analysis', 'write_ai_content', 'edit_with_ai', 'analyze_structure', 'list_and_suggest']
          };
      }
    } catch (error) {
      return {
        success: false,
        error: `智能文件操作失败: ${error instanceof Error ? error.message : '未知错误'}`
      };
    }
  }
};