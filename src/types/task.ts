export interface Task {
  id: string;
  title: string;
  description: string;
  complexity: TaskComplexity;
  status: TaskStatus;
  priority: TaskPriority;
  
  // 时间信息
  createdAt: Date;
  startedAt?: Date;
  completedAt?: Date;
  estimatedDuration?: number; // 分钟
  
  // 分解信息
  parentTaskId?: string;
  subtasks: Task[];
  currentStep: number;
  totalSteps: number;
  
  // 执行信息
  executionPlan?: TaskExecutionPlan;
  executionLog: TaskLogEntry[];
  context: TaskContext;
  
  // 用户交互
  requiresUserInput: boolean;
  userNotes: string[];
}

export interface TaskExecutionPlan {
  id: string;
  title: string;
  description: string;
  reasoning: string; // deepseek-reasoner 的思考过程
  steps: TaskStep[];
  dependencies: string[]; // 依赖的工具或条件
  estimatedTime: number;
  riskAssessment: string;
}

export interface TaskStep {
  id: string;
  title: string;
  description: string;
  type: TaskStepType;
  status: TaskStatus;
  
  // 执行信息
  toolCalls: string[]; // 需要调用的工具
  expectedOutput: string;
  actualOutput?: string;
  
  // 时间信息
  estimatedDuration: number;
  startedAt?: Date;
  completedAt?: Date;
  
  // 依赖关系
  dependencies: string[]; // 依赖的其他步骤
  blockers: string[]; // 阻塞因素
  
  // 验证
  successCriteria: string[];
  validationResult?: TaskValidationResult;
}

export interface TaskContext {
  projectPath: string;
  relevantFiles: string[];
  environmentInfo: Record<string, any>;
  userPreferences: Record<string, any>;
  previousTasks: string[]; // 相关的历史任务ID
}

export interface TaskLogEntry {
  id: string;
  timestamp: Date;
  type: TaskLogType;
  stepId?: string;
  message: string;
  details?: any;
  modelUsed?: string; // 使用的AI模型
}

export interface TaskValidationResult {
  success: boolean;
  score: number; // 0-100
  details: string;
  suggestions: string[];
}

export type TaskComplexity = 'simple' | 'moderate' | 'complex' | 'very_complex';

export type TaskStatus = 
  | 'pending'     // 等待开始
  | 'planning'    // 规划中（deepseek-reasoner）
  | 'ready'       // 计划完成，准备执行
  | 'in_progress' // 执行中
  | 'paused'      // 暂停
  | 'waiting_input' // 等待用户输入
  | 'completed'   // 完成
  | 'failed'      // 失败
  | 'cancelled';  // 取消

export type TaskPriority = 'low' | 'medium' | 'high' | 'urgent';

export type TaskStepType = 
  | 'analysis'    // 分析任务
  | 'file_operation' // 文件操作
  | 'code_generation' // 代码生成
  | 'testing'     // 测试
  | 'validation'  // 验证
  | 'deployment'  // 部署
  | 'documentation' // 文档
  | 'research'    // 研究
  | 'user_interaction' // 用户交互
  | 'tool_execution'; // 工具执行

export type TaskLogType = 
  | 'info'
  | 'success' 
  | 'warning'
  | 'error'
  | 'user_input'
  | 'ai_reasoning'
  | 'tool_call'
  | 'validation';

// 任务复杂度分析结果
export interface TaskComplexityAnalysis {
  complexity: TaskComplexity;
  score: number; // 0-100
  factors: ComplexityFactor[];
  recommendation: string;
  requiresDecomposition: boolean;
  estimatedTime: number; // 分钟
}

export interface ComplexityFactor {
  name: string;
  score: number;
  weight: number;
  description: string;
}

// 任务类型分类
export type TaskCategory = 
  | 'development'     // 开发任务
  | 'analysis'        // 分析任务  
  | 'documentation'   // 文档任务
  | 'debugging'       // 调试任务
  | 'optimization'    // 优化任务
  | 'research'        // 研究任务
  | 'maintenance'     // 维护任务
  | 'deployment'      // 部署任务
  | 'testing'         // 测试任务
  | 'refactoring';    // 重构任务

// 任务创建请求
export interface CreateTaskRequest {
  description: string;
  priority?: TaskPriority;
  context?: Partial<TaskContext>;
  userPreferences?: Record<string, any>;
}

// 任务更新请求
export interface UpdateTaskRequest {
  taskId: string;
  status?: TaskStatus;
  userNotes?: string[];
  context?: Partial<TaskContext>;
}

// 任务搜索/过滤条件
export interface TaskFilter {
  status?: TaskStatus[];
  complexity?: TaskComplexity[];
  priority?: TaskPriority[];
  category?: TaskCategory[];
  dateRange?: {
    start: Date;
    end: Date;
  };
  searchText?: string;
}

// AI 模型配置
export interface TaskAIConfig {
  planningModel: string;    // 用于规划的模型 (deepseek-reasoner)
  executionModel: string;   // 用于执行的模型 (deepseek-chat)
  maxPlanningTokens: number;
  maxExecutionTokens: number;
  temperature: number;
}