import { Chat<PERSON>essage, ChatSession, ModelProvider, ToolCall } from '../types';
import { ConfigManager } from './config';
import { OpenAIProvider } from '../models/openai';
import { AnthropicProvider } from '../models/anthropic';
import { DeepSeekProvider } from '../models/deepseek';
import { MemoryManager } from './memory';
import { ToolManager } from './toolManager';
import { v4 as uuidv4 } from 'uuid';

export class ChatManager {
  private currentSession: ChatSession;
  private providers: Map<string, ModelProvider> = new Map();
  private memory: MemoryManager;
  private toolManager: ToolManager;
  private static sharedToolManager: ToolManager | null = null;
  
  constructor(private config: ConfigManager) {
    this.memory = new MemoryManager();
    
    // 使用单例模式避免重复注册工具
    if (!ChatManager.sharedToolManager) {
      ChatManager.sharedToolManager = new ToolManager();
    }
    this.toolManager = ChatManager.sharedToolManager;
    
    this.initializeProviders();
    this.currentSession = {
      id: uuidv4(),
      title: '新会话',
      messages: [],
      createdAt: new Date(),
      updatedAt: new Date()
    };
  }

  private initializeProviders(): void {
    const userConfig = this.config.getConfig();
    
    // 初始化 OpenAI 提供商
    if (userConfig.apiKeys.openai) {
      for (const [modelName, modelConfig] of Object.entries(userConfig.models)) {
        if (modelConfig.provider === 'openai') {
          this.providers.set(modelName, new OpenAIProvider(
            userConfig.apiKeys.openai,
            modelConfig.model,
            modelConfig.temperature,
            modelConfig.maxTokens
          ));
        }
      }
    }

    // 初始化 Anthropic 提供商
    if (userConfig.apiKeys.anthropic) {
      for (const [modelName, modelConfig] of Object.entries(userConfig.models)) {
        if (modelConfig.provider === 'anthropic') {
          this.providers.set(modelName, new AnthropicProvider(
            userConfig.apiKeys.anthropic,
            modelConfig.model,
            modelConfig.temperature,
            modelConfig.maxTokens
          ));
        }
      }
    }

    // 初始化 DeepSeek 提供商
    if (userConfig.apiKeys.deepseek) {
      for (const [modelName, modelConfig] of Object.entries(userConfig.models)) {
        if (modelConfig.provider === 'deepseek') {
          this.providers.set(modelName, new DeepSeekProvider(
            userConfig.apiKeys.deepseek,
            modelConfig.model,
            modelConfig.temperature,
            modelConfig.maxTokens
          ));
        }
      }
    }
  }

  private analyzeMessageComplexity(content: string): 'simple' | 'complex' {
    // 关键词匹配 - 复杂问题指标
    const complexKeywords = [
      // 分析推理类
      '分析', '推理', '解释为什么', '原理', '原因', '机制', '逻辑', '思考', '判断',
      // 对比评估类  
      '比较', '对比', '评估', '优缺点', '差异', '区别', '选择', '建议',
      // 创作规划类
      '设计', '规划', '策略', '方案', '计划', '创作', '写作', '构思',
      // 问题解决类
      '解决', '处理', '应对', '改进', '优化', '修复', '调试', '排查',
      // 学术专业类
      '研究', '探讨', '深入', '详细', '系统', '全面', '专业', '学术',
      // 英文关键词
      'analyze', 'explain', 'compare', 'design', 'strategy', 'complex', 'detailed'
    ];

    const simpleKeywords = [
      // 基础查询类
      '什么是', '怎么', '如何', '是什么', '定义', '介绍',
      // 简单操作类
      '帮我', '给我', '列出', '显示', '查看', '打开',
      // 确认回答类
      '是吗', '对吗', '可以吗', '能吗', '会吗',
      // 英文简单词
      'what', 'how', 'show', 'list', 'help'
    ];

    // 长度指标
    const isLong = content.length > 100;
    
    // 标点符号复杂度（多个问号、感叹号等）
    const complexPunctuation = /[？！?!]{2,}|[，。,\.]{3,}/.test(content);
    
    // 包含多个问题
    const multipleQuestions = (content.match(/[？?]/g) || []).length > 1;

    // 计算复杂度得分
    let complexityScore = 0;

    // 复杂关键词匹配
    complexKeywords.forEach(keyword => {
      if (content.includes(keyword)) {
        complexityScore += 2;
      }
    });

    // 简单关键词匹配（负分）
    simpleKeywords.forEach(keyword => {
      if (content.includes(keyword)) {
        complexityScore -= 1;
      }
    });

    // 长度加分
    if (isLong) complexityScore += 1;
    
    // 复杂标点加分
    if (complexPunctuation) complexityScore += 1;
    
    // 多问题加分
    if (multipleQuestions) complexityScore += 2;

    // 判断阈值
    return complexityScore > 2 ? 'complex' : 'simple';
  }

  private selectOptimalModel(content: string, requestedModel?: string): string {
    // 如果用户指定了模型，直接使用
    if (requestedModel && requestedModel !== 'auto') {
      return requestedModel;
    }

    const defaultModel = this.config.getConfig().defaultModel;
    
    // 如果默认模型不是 auto，直接使用
    if (defaultModel !== 'auto') {
      return defaultModel;
    }

    // 智能选择模型
    const complexity = this.analyzeMessageComplexity(content);
    
    if (complexity === 'complex') {
      // 复杂问题优先使用 deepseek-reasoner
      return this.providers.has('deepseek-reasoner') ? 'deepseek-reasoner' : 'deepseek-chat';
    } else {
      // 简单问题使用 deepseek-chat
      return this.providers.has('deepseek-chat') ? 'deepseek-chat' : 'deepseek-reasoner';
    }
  }

  public async sendMessage(content: string, modelName?: string, abortController?: AbortController): Promise<{ response: string; selectedModel: string }> {
    const selectedModel = this.selectOptimalModel(content, modelName);
    const provider = this.providers.get(selectedModel);

    if (!provider) {
      throw new Error(`模型 "${selectedModel}" 不可用。请检查配置和 API 密钥。`);
    }

    if (!provider.isAvailable()) {
      throw new Error(`模型提供商 "${provider.name}" 不可用。请检查 API 密钥配置。`);
    }

    // 添加用户消息
    const userMessage: ChatMessage = {
      role: 'user',
      content,
      timestamp: new Date()
    };
    this.currentSession.messages.push(userMessage);

    try {
      // 执行对话循环，处理工具调用
      const finalResponse = await this.executeConversationLoop(provider, abortController);
      
      this.currentSession.updatedAt = new Date();
      return { response: finalResponse, selectedModel };
    } catch (error) {
      // 检查是否是用户中断
      if (abortController?.signal.aborted) {
        // 移除用户消息，因为对话被中断
        this.currentSession.messages.pop();
        throw new Error('AI 思考已被用户中断');
      }
      
      // 如果 AI 调用失败，移除用户消息
      this.currentSession.messages.pop();
      throw error;
    }
  }

  private async executeConversationLoop(provider: ModelProvider, abortController?: AbortController): Promise<string> {
    const maxIterations = 10; // 防止无限循环
    let iterations = 0;
    let finalResponse = '';

    while (iterations < maxIterations) {
      iterations++;

      // 检查是否被中断
      if (abortController?.signal.aborted) {
        throw new Error('AI 思考已被用户中断');
      }

      // 准备消息（包含记忆上下文）
      const messagesWithMemory = await this.prepareMessagesWithMemory('');

      // 获取可用工具（仅当提供商支持时）
      const tools = provider.supportsTools() ? this.toolManager.getAvailableTools() : undefined;

      // 检查是否被中断
      if (abortController?.signal.aborted) {
        throw new Error('AI 思考已被用户中断');
      }

      // 调用 AI 模型
      const response = await provider.chat(messagesWithMemory, tools);
      
      finalResponse = response.content;

      // 添加 AI 回复到会话
      const assistantMessage: ChatMessage = {
        role: 'assistant',
        content: response.content,
        timestamp: new Date(),
        toolCalls: response.toolCalls
      };
      this.currentSession.messages.push(assistantMessage);

      // 检查是否需要执行工具调用
      if (response.toolCalls && response.toolCalls.length > 0) {
        console.log(`AI 请求执行 ${response.toolCalls.length} 个工具调用`);
        
        // 执行所有工具调用
        for (const toolCall of response.toolCalls) {
          // 检查是否被中断
          if (abortController?.signal.aborted) {
            throw new Error('AI 思考已被用户中断');
          }
          
          console.log(`🔧 执行工具: ${toolCall.name}`);
          const toolResult = await this.toolManager.executeTool(toolCall.name, toolCall.arguments);
          console.log(`✅ 工具结果: ${toolResult.success ? '成功' : '失败'}`);
          
          // 为AI格式化工具结果，使其更容易理解
          let formattedContent: string;
          if (toolResult.success) {
            // 根据工具类型格式化结果
            if (toolCall.name === 'read_file' && toolResult.content) {
              formattedContent = `文件读取成功 (${toolResult.path}):
文件大小: ${toolResult.size} 字节
行数: ${toolResult.lines} 行

文件内容:
${toolResult.content}`;
            } else if (toolCall.name === 'check_permissions' && toolResult.permissions) {
              formattedContent = `权限检查成功 (${toolResult.path}):
类型: ${toolResult.type}
权限: 读取${toolResult.permissions.readable ? '✅' : '❌'} 写入${toolResult.permissions.writable ? '✅' : '❌'} 执行${toolResult.permissions.executable ? '✅' : '❌'}
权限模式: ${toolResult.mode}
文件大小: ${toolResult.size} 字节`;
            } else {
              formattedContent = `工具执行成功:\n${JSON.stringify(toolResult, null, 2)}`;
            }
          } else {
            formattedContent = `工具执行失败: ${toolResult.error}`;
          }
          
          // 添加工具执行结果到会话
          const toolMessage: ChatMessage = {
            role: 'tool',
            content: formattedContent,
            timestamp: new Date(),
            toolCallId: toolCall.id
          };
          this.currentSession.messages.push(toolMessage);
        }
        
        // 继续循环，让 AI 处理工具结果
        continue;
      }

      // 没有工具调用，对话完成
      break;
    }

    if (iterations >= maxIterations) {
      console.warn('工具调用循环达到最大迭代次数，强制退出');
    }

    return finalResponse;
  }

  private async prepareMessagesWithMemory(userInput: string): Promise<ChatMessage[]> {
    const messages: ChatMessage[] = [];

    // 检查是否有项目记忆
    try {
      const hasMemory = await this.memory.hasMemory();
      if (hasMemory) {
        const recentEntries = await this.memory.getRecentEntries(3);
        
        if (recentEntries.length > 0) {
          // 添加系统消息，包含项目记忆
          let memoryContext = '# 项目上下文记忆\n\n';
          
          // 添加项目扫描信息（如果有的话）
          const scanEntries = recentEntries.filter(e => e.type === 'scan');
          if (scanEntries.length > 0) {
            memoryContext += '## 项目结构信息\n';
            memoryContext += scanEntries[0].content + '\n\n';
          }
          
          // 添加最近的洞察和重要信息
          const insightEntries = recentEntries.filter(e => e.type === 'insight').slice(0, 2);
          if (insightEntries.length > 0) {
            memoryContext += '## 重要洞察\n';
            insightEntries.forEach(entry => {
              memoryContext += `- ${entry.content}\n`;
            });
            memoryContext += '\n';
          }

          memoryContext += '请基于以上项目信息来回答用户问题。';

          messages.push({
            role: 'system',
            content: memoryContext,
            timestamp: new Date()
          });
        }
      }
    } catch (error) {
      // 记忆加载失败不影响正常对话
      console.warn('加载项目记忆失败:', error);
    }

    // 添加当前会话的消息
    messages.push(...this.currentSession.messages);

    return messages;
  }

  public getCurrentSession(): ChatSession {
    return this.currentSession;
  }

  public newSession(title?: string): void {
    this.currentSession = {
      id: uuidv4(),
      title: title || '新会话',
      messages: [],
      createdAt: new Date(),
      updatedAt: new Date()
    };
  }

  public getAvailableModels(): string[] {
    return Array.from(this.providers.keys()).filter(model => 
      this.providers.get(model)?.isAvailable()
    );
  }

  public addSystemMessage(content: string): void {
    const systemMessage: ChatMessage = {
      role: 'system',
      content,
      timestamp: new Date()
    };
    this.currentSession.messages.unshift(systemMessage);
  }

  public async addInsight(insight: string): Promise<void> {
    await this.memory.addInsight(insight);
  }

  public async addTodo(todo: string): Promise<void> {
    await this.memory.addTodo(todo);
  }

  public async saveConversationSummary(): Promise<void> {
    if (this.currentSession.messages.length > 0) {
      const lastFewMessages = this.currentSession.messages.slice(-6);
      const summary = `会话摘要 (${new Date().toLocaleString()})\n` +
        lastFewMessages.map(msg => `${msg.role}: ${msg.content.slice(0, 100)}...`).join('\n');
      
      await this.memory.addConversationSummary(summary);
    }
  }

  public getMemoryManager(): MemoryManager {
    return this.memory;
  }

  public getToolManager(): ToolManager {
    return this.toolManager;
  }
}