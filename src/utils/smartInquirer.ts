import inquirer from 'inquirer';
import chalk from 'chalk';
import { SmartAutoComplete, SmartSuggestion } from './smartAutoComplete';
import { SmartSuggestionDisplay } from './smartSuggestionDisplay';

export class SmartInquirer {
  private autoComplete: SmartAutoComplete;
  private lastSuggestions: SmartSuggestion[] = [];

  constructor(projectPath: string) {
    this.autoComplete = new SmartAutoComplete(projectPath);
  }

  /**
   * 创建智能输入提示
   */
  public async createSmartInput(message: string): Promise<string> {
    // 使用简化的单次输入，注重实用性
    try {
      const result = await inquirer.prompt({
        type: 'input',
        name: 'input',
        message: this.formatMessage(message, ''),
        validate: (input: string) => {
          return input.trim().length > 0 || '请输入内容';
        }
      });
      
      const input = result.input.trim();
      
      // 处理特殊输入
      if (input === '?') {
        SmartSuggestionDisplay.showQuickActions();
        return this.createSmartInput(message);
      }
      
      if (input === '??') {
        const suggestions = this.autoComplete.getSmartSuggestions('');
        if (suggestions.length > 0) {
          const selected = await SmartSuggestionDisplay.showInteractiveSuggestions('', suggestions);
          if (selected && selected !== '__CONTINUE__') {
            this.autoComplete.addToHistory(selected);
            return selected;
          }
        } else {
          SmartSuggestionDisplay.showQuickActions();
        }
        return this.createSmartInput(message);
      }
      
      // 添加到历史记录
      if (input) {
        this.autoComplete.addToHistory(input);
        
        // 输入后显示相关建议（仅提示，不强制选择）
        const suggestions = this.autoComplete.getSmartSuggestions(input);
        if (suggestions.length > 0 && input.length > 2) {
          console.log(chalk.gray(`💡 下次可以试试: ${suggestions.slice(0, 2).map(s => s.text).join(', ')}`));
        }
      }
      
      return input;
      
    } catch (error) {
      if (error instanceof Error && error.name === 'ExitPromptError') {
        throw error;
      }
      const errorMessage = error instanceof Error ? error.message : '未知错误';
      console.log(chalk.red(`输入错误: ${errorMessage}`));
      return this.createSmartInput(message);
    }
  }

  /**
   * 处理 Tab 补全
   */
  private async handleTabCompletion(input: string, suggestions: SmartSuggestion[]): Promise<string | null> {
    if (suggestions.length === 0) return null;

    if (suggestions.length === 1) {
      // 只有一个建议，直接使用
      return suggestions[0].text;
    }

    // 多个建议，显示选择菜单
    return await SmartSuggestionDisplay.showInteractiveSuggestions(input, suggestions);
  }

  /**
   * 判断是否应该显示建议
   */
  private shouldShowSuggestions(input: string, suggestions: SmartSuggestion[]): boolean {
    // 输入长度大于2个字符时显示建议
    if (input.length < 2) return false;
    
    // 有高优先级建议时显示
    if (suggestions.some(s => s.priority >= 8)) return true;
    
    // 建议数量适中时显示
    return suggestions.length >= 2 && suggestions.length <= 10;
  }

  /**
   * 格式化输入提示消息
   */
  private formatMessage(baseMessage: string, currentInput: string): string {
    if (!currentInput) {
      return baseMessage + chalk.gray(' (输入 ? 查看快速操作)');
    }

    // 根据输入类型给出提示
    if (currentInput.startsWith('/')) {
      return baseMessage + chalk.gray(' (命令模式)');
    } else if (currentInput.startsWith('!')) {
      return baseMessage + chalk.gray(' (系统命令)');
    } else if (currentInput.startsWith('@')) {
      return baseMessage + chalk.gray(' (文件操作)');
    } else if (currentInput.startsWith('#')) {
      return baseMessage + chalk.gray(' (项目记忆)');
    } else {
      return baseMessage + chalk.gray(' (AI 对话)');
    }
  }

  /**
   * 获取命令历史
   */
  public getHistory(): string[] {
    return this.autoComplete.getHistory();
  }

  /**
   * 添加到历史记录
   */
  public addToHistory(command: string): void {
    this.autoComplete.addToHistory(command);
  }

  /**
   * 获取最近的建议
   */
  public getLastSuggestions(): SmartSuggestion[] {
    return this.lastSuggestions;
  }

  /**
   * 显示输入统计
   */
  public showInputStats(): void {
    const history = this.autoComplete.getHistory();
    const commandCount = history.filter(cmd => cmd.startsWith('/')).length;
    const bashCount = history.filter(cmd => cmd.startsWith('!')).length;
    const fileCount = history.filter(cmd => cmd.startsWith('@')).length;
    const memoryCount = history.filter(cmd => cmd.startsWith('#')).length;
    const chatCount = history.length - commandCount - bashCount - fileCount - memoryCount;

    console.log(chalk.blue('\n📊 使用统计:'));
    console.log(`  💬 AI 对话: ${chatCount} 次`);
    console.log(`  🔧 命令执行: ${commandCount} 次`);
    console.log(`  ⚡ 系统命令: ${bashCount} 次`);
    console.log(`  📁 文件操作: ${fileCount} 次`);
    console.log(`  🧠 记忆操作: ${memoryCount} 次`);
    console.log(`  📚 总历史: ${history.length} 条\n`);
  }
}