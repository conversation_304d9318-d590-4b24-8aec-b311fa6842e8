import inquirer from 'inquirer';
import chalk from 'chalk';
import { existsSync, readdirSync, statSync } from 'fs';
import { join, dirname, basename } from 'path';

export interface SmartSuggestion {
  text: string;
  description?: string;
  type: 'command' | 'file' | 'bash' | 'memory' | 'history' | 'ai_template';
  priority: number;
  icon?: string;
}

export class SmartAutoComplete {
  private commandHistory: string[] = [];
  private projectPath: string;
  private recentFiles: string[] = [];
  
  // 常用命令模板
  private aiTemplates = [
    { text: '帮我分析这个项目的架构', type: 'ai_template', description: '项目架构分析' },
    { text: '解释这段代码的作用', type: 'ai_template', description: '代码解释' },
    { text: '优化这个函数的性能', type: 'ai_template', description: '性能优化建议' },
    { text: '找出可能的bug', type: 'ai_template', description: 'Bug 检测' },
    { text: '重构这段代码', type: 'ai_template', description: '代码重构' },
    { text: '添加单元测试', type: 'ai_template', description: '测试用例生成' },
    { text: '写文档注释', type: 'ai_template', description: '文档生成' },
    { text: '检查安全漏洞', type: 'ai_template', description: '安全审计' }
  ];

  private bashTemplates = [
    { text: 'git status', type: 'bash', description: '查看Git状态' },
    { text: 'git add .', type: 'bash', description: '暂存所有更改' },
    { text: 'git commit -m ""', type: 'bash', description: '提交更改' },
    { text: 'git push', type: 'bash', description: '推送到远程' },
    { text: 'npm install', type: 'bash', description: '安装依赖' },
    { text: 'npm run build', type: 'bash', description: '构建项目' },
    { text: 'npm run dev', type: 'bash', description: '开发模式' },
    { text: 'npm test', type: 'bash', description: '运行测试' },
    { text: 'docker build -t', type: 'bash', description: '构建Docker镜像' },
    { text: 'docker run', type: 'bash', description: '运行容器' }
  ];

  private commands = [
    { text: 'help', type: 'command', description: '显示帮助信息' },
    { text: 'init', type: 'command', description: '初始化项目记忆' },
    { text: 'config', type: 'command', description: '配置API密钥' },
    { text: 'models', type: 'command', description: '查看可用模型' },
    { text: 'memory', type: 'command', description: '查看项目记忆' },
    { text: 'history', type: 'command', description: '查看命令历史' },
    { text: 'shortcuts', type: 'command', description: '显示快捷键' },
    { text: 'clear', type: 'command', description: '清除屏幕' },
    { text: 'exit', type: 'command', description: '退出程序' }
  ];

  constructor(projectPath: string = process.cwd()) {
    this.projectPath = projectPath;
    this.loadHistory();
    this.scanRecentFiles();
  }

  /**
   * 获取智能建议
   */
  public getSmartSuggestions(input: string): SmartSuggestion[] {
    const trimmed = input.trim().toLowerCase();
    const suggestions: SmartSuggestion[] = [];

    if (!trimmed) {
      // 空输入时显示最常用的建议
      return this.getDefaultSuggestions();
    }

    // 根据前缀确定建议类型
    if (trimmed.startsWith('/')) {
      return this.getCommandSuggestions(trimmed.slice(1));
    }
    
    if (trimmed.startsWith('!')) {
      return this.getBashSuggestions(trimmed.slice(1));
    }
    
    if (trimmed.startsWith('@')) {
      return this.getFileSuggestions(trimmed.slice(1));
    }
    
    if (trimmed.startsWith('#')) {
      return this.getMemorySuggestions(trimmed.slice(1));
    }

    // 普通文本输入的智能建议
    return this.getContextualSuggestions(trimmed);
  }

  private getDefaultSuggestions(): SmartSuggestion[] {
    return [
      { text: '/help', type: 'command', description: '查看帮助', priority: 10, icon: '❓' },
      { text: '/exit', type: 'command', description: '退出程序', priority: 9, icon: '👋' },
      { text: '帮我分析这个项目', type: 'ai_template', description: 'AI项目分析', priority: 8, icon: '🔍' },
      { text: '@package.json', type: 'file', description: '查看项目配置', priority: 7, icon: '📦' },
      { text: '!git status', type: 'bash', description: '查看Git状态', priority: 6, icon: '🔧' },
      { text: '#记录重要发现', type: 'memory', description: '添加洞察', priority: 5, icon: '💡' }
    ];
  }

  private getCommandSuggestions(partial: string): SmartSuggestion[] {
    return this.commands
      .filter(cmd => cmd.text.toLowerCase().includes(partial))
      .map(cmd => ({
        text: `/${cmd.text}`,
        description: cmd.description,
        type: cmd.type as any,
        priority: this.calculatePriority(cmd.text, partial),
        icon: this.getCommandIcon(cmd.text)
      }))
      .sort((a, b) => b.priority - a.priority)
      .slice(0, 8);
  }

  private getBashSuggestions(partial: string): SmartSuggestion[] {
    const suggestions = this.bashTemplates
      .filter(cmd => cmd.text.toLowerCase().includes(partial))
      .map(cmd => ({
        text: `!${cmd.text}`,
        description: cmd.description,
        type: cmd.type as any,
        priority: this.calculatePriority(cmd.text, partial),
        icon: '🔧'
      }));

    // 添加历史命令中的bash命令
    const bashHistory = this.commandHistory
      .filter(cmd => cmd.startsWith('!') && cmd.toLowerCase().includes(partial))
      .slice(0, 3)
      .map(cmd => ({
        text: cmd,
        description: '历史命令',
        type: 'history' as any,
        priority: 5,
        icon: '📚'
      }));

    return [...suggestions, ...bashHistory]
      .sort((a, b) => b.priority - a.priority)
      .slice(0, 8);
  }

  private getFileSuggestions(partial: string): SmartSuggestion[] {
    const suggestions: SmartSuggestion[] = [];

    try {
      // 获取当前目录的文件
      const currentDir = partial.includes('/') ? 
        join(this.projectPath, dirname(partial)) : 
        this.projectPath;

      if (existsSync(currentDir)) {
        const files = readdirSync(currentDir)
          .filter(file => {
            const fileName = basename(partial) || partial;
            return file.toLowerCase().includes(fileName.toLowerCase());
          })
          .slice(0, 6);

        for (const file of files) {
          const fullPath = join(currentDir, file);
          const stat = statSync(fullPath);
          const relativePath = partial.includes('/') ? 
            join(dirname(partial), file) : 
            file;

          suggestions.push({
            text: `@${relativePath}`,
            description: stat.isDirectory() ? '目录' : this.getFileDescription(file),
            type: 'file',
            priority: this.getFilePriority(file, stat.isDirectory()),
            icon: stat.isDirectory() ? '📁' : this.getFileIcon(file)
          });
        }
      }

      // 添加常用文件建议
      const commonFiles = ['package.json', 'README.md', 'tsconfig.json', '.gitignore', 'src/', 'dist/'];
      for (const file of commonFiles) {
        if (file.toLowerCase().includes(partial.toLowerCase()) && 
            existsSync(join(this.projectPath, file))) {
          suggestions.push({
            text: `@${file}`,
            description: this.getFileDescription(file),
            type: 'file',
            priority: 8,
            icon: this.getFileIcon(file)
          });
        }
      }

    } catch (error) {
      // 静默处理错误
    }

    return suggestions
      .sort((a, b) => b.priority - a.priority)
      .slice(0, 8);
  }

  private getMemorySuggestions(partial: string): SmartSuggestion[] {
    const memoryActions = [
      { text: '#view', description: '查看项目记忆', priority: 8 },
      { text: '#todo ', description: '添加待办事项', priority: 7 },
      { text: '#insight ', description: '记录重要洞察', priority: 7 },
      { text: '#search ', description: '搜索记忆内容', priority: 6 },
      { text: '#clear', description: '清除记忆', priority: 5 }
    ];

    if (!partial) {
      return memoryActions.map(action => ({
        text: action.text,
        description: action.description,
        type: 'memory' as any,
        priority: action.priority,
        icon: '🧠'
      }));
    }

    // 如果已经输入了内容，提供模板建议
    const templates = [
      '发现了新的架构模式',
      '重要的性能优化点',
      '需要重构的代码部分',
      '安全风险提醒',
      '技术债务记录'
    ];

    return templates
      .filter(template => template.toLowerCase().includes(partial.toLowerCase()))
      .map(template => ({
        text: `#${template}`,
        description: '洞察模板',
        type: 'memory' as any,
        priority: 6,
        icon: '💡'
      }))
      .slice(0, 5);
  }

  private getContextualSuggestions(input: string): SmartSuggestion[] {
    const suggestions: SmartSuggestion[] = [];

    // AI 模板建议
    const relevantTemplates = this.aiTemplates
      .filter(template => this.isRelevant(template.text, input))
      .map(template => ({
        text: template.text,
        description: template.description,
        type: template.type as any,
        priority: 7,
        icon: '🤖'
      }));

    // 历史记录建议
    const historyMatches = this.commandHistory
      .filter(cmd => !cmd.startsWith('/') && !cmd.startsWith('!') && 
               this.isRelevant(cmd, input))
      .slice(0, 3)
      .map(cmd => ({
        text: cmd,
        description: '历史记录',
        type: 'history' as any,
        priority: 5,
        icon: '📚'
      }));

    // 基于输入内容的智能建议
    const contextualSuggestions = this.generateContextualSuggestions(input);

    return [...relevantTemplates, ...historyMatches, ...contextualSuggestions]
      .sort((a, b) => b.priority - a.priority)
      .slice(0, 8);
  }

  private generateContextualSuggestions(input: string): SmartSuggestion[] {
    const suggestions: SmartSuggestion[] = [];
    const lowercaseInput = input.toLowerCase();

    // 代码相关关键词
    if (lowercaseInput.includes('代码') || lowercaseInput.includes('code')) {
      suggestions.push(
        { text: '帮我重构这段代码，提高可读性', description: '代码重构', type: 'ai_template', priority: 8, icon: '🔧' },
        { text: '分析这段代码的复杂度', description: '复杂度分析', type: 'ai_template', priority: 7, icon: '📊' }
      );
    }

    // 项目相关关键词
    if (lowercaseInput.includes('项目') || lowercaseInput.includes('project')) {
      suggestions.push(
        { text: '@package.json 分析项目依赖', description: '依赖分析', type: 'file', priority: 8, icon: '📦' },
        { text: '帮我制定项目的开发计划', description: '项目规划', type: 'ai_template', priority: 7, icon: '📋' }
      );
    }

    // 错误相关关键词
    if (lowercaseInput.includes('错误') || lowercaseInput.includes('bug') || lowercaseInput.includes('error')) {
      suggestions.push(
        { text: '帮我调试这个错误', description: '错误调试', type: 'ai_template', priority: 8, icon: '🐛' },
        { text: '分析可能的bug原因', description: 'Bug分析', type: 'ai_template', priority: 7, icon: '🔍' }
      );
    }

    // 性能相关关键词
    if (lowercaseInput.includes('性能') || lowercaseInput.includes('performance') || lowercaseInput.includes('优化')) {
      suggestions.push(
        { text: '分析性能瓶颈', description: '性能分析', type: 'ai_template', priority: 8, icon: '⚡' },
        { text: '提供性能优化建议', description: '优化建议', type: 'ai_template', priority: 7, icon: '🚀' }
      );
    }

    return suggestions;
  }

  private isRelevant(text: string, input: string): boolean {
    const textLower = text.toLowerCase();
    const inputLower = input.toLowerCase();
    
    // 简单的相关性检查
    const inputWords = inputLower.split(/\s+/);
    return inputWords.some(word => word.length > 2 && textLower.includes(word));
  }

  private calculatePriority(text: string, partial: string): number {
    let priority = 5;
    
    // 完全匹配优先级最高
    if (text.toLowerCase() === partial.toLowerCase()) return 10;
    
    // 开头匹配
    if (text.toLowerCase().startsWith(partial.toLowerCase())) priority += 3;
    
    // 包含匹配
    if (text.toLowerCase().includes(partial.toLowerCase())) priority += 1;
    
    // 常用命令优先级加成
    const commonCommands = ['help', 'exit', 'config', 'init'];
    if (commonCommands.includes(text.toLowerCase())) priority += 2;
    
    return priority;
  }

  private getFilePriority(fileName: string, isDir: boolean): number {
    let priority = 5;
    
    if (isDir) priority += 1;
    
    // 重要文件优先级加成
    const importantFiles = ['package.json', 'readme.md', 'tsconfig.json', 'dockerfile'];
    if (importantFiles.some(file => fileName.toLowerCase().includes(file))) {
      priority += 3;
    }
    
    // 源码文件优先级加成
    const codeExtensions = ['.ts', '.js', '.tsx', '.jsx', '.py', '.java', '.cpp'];
    if (codeExtensions.some(ext => fileName.toLowerCase().endsWith(ext))) {
      priority += 2;
    }
    
    return priority;
  }

  private getFileDescription(fileName: string): string {
    const ext = fileName.toLowerCase();
    
    if (ext.endsWith('.json')) return 'JSON配置文件';
    if (ext.endsWith('.md')) return 'Markdown文档';
    if (ext.endsWith('.ts')) return 'TypeScript源码';
    if (ext.endsWith('.js')) return 'JavaScript源码';
    if (ext.endsWith('.tsx') || ext.endsWith('.jsx')) return 'React组件';
    if (ext.endsWith('.py')) return 'Python脚本';
    if (ext.endsWith('.css')) return '样式文件';
    if (ext.endsWith('.html')) return 'HTML页面';
    if (ext.endsWith('.yml') || ext.endsWith('.yaml')) return 'YAML配置';
    if (ext === 'dockerfile') return 'Docker配置';
    if (ext === '.gitignore') return 'Git忽略规则';
    
    return '文件';
  }

  private getFileIcon(fileName: string): string {
    const ext = fileName.toLowerCase();
    
    if (ext.endsWith('.json')) return '📄';
    if (ext.endsWith('.md')) return '📝';
    if (ext.endsWith('.ts') || ext.endsWith('.js')) return '📜';
    if (ext.endsWith('.tsx') || ext.endsWith('.jsx')) return '⚛️';
    if (ext.endsWith('.py')) return '🐍';
    if (ext.endsWith('.css')) return '🎨';
    if (ext.endsWith('.html')) return '🌐';
    if (ext.endsWith('.yml') || ext.endsWith('.yaml')) return '⚙️';
    if (ext === 'dockerfile') return '🐳';
    if (ext === '.gitignore') return '🚫';
    
    return '📄';
  }

  private getCommandIcon(command: string): string {
    const iconMap: { [key: string]: string } = {
      'help': '❓',
      'exit': '👋',
      'config': '⚙️',
      'init': '🚀',
      'models': '🤖',
      'memory': '🧠',
      'history': '📚',
      'shortcuts': '⌨️',
      'clear': '🧹'
    };
    
    return iconMap[command] || '💻';
  }

  private loadHistory(): void {
    try {
      const historyFile = join(this.projectPath, '.chater_history');
      if (existsSync(historyFile)) {
        const fs = require('fs');
        const content = fs.readFileSync(historyFile, 'utf8');
        this.commandHistory = JSON.parse(content) || [];
      }
    } catch (error) {
      this.commandHistory = [];
    }
  }

  private scanRecentFiles(): void {
    try {
      // 扫描最近修改的文件
      this.recentFiles = readdirSync(this.projectPath)
        .filter(file => {
          try {
            const stat = statSync(join(this.projectPath, file));
            return stat.isFile() && !file.startsWith('.');
          } catch {
            return false;
          }
        })
        .sort((a, b) => {
          try {
            const statA = statSync(join(this.projectPath, a));
            const statB = statSync(join(this.projectPath, b));
            return statB.mtime.getTime() - statA.mtime.getTime();
          } catch {
            return 0;
          }
        })
        .slice(0, 10);
    } catch (error) {
      this.recentFiles = [];
    }
  }

  public addToHistory(command: string): void {
    if (!command.trim()) return;
    
    // 移除重复项
    const index = this.commandHistory.indexOf(command);
    if (index > -1) {
      this.commandHistory.splice(index, 1);
    }
    
    // 添加到开头
    this.commandHistory.unshift(command);
    
    // 保持历史记录大小
    if (this.commandHistory.length > 100) {
      this.commandHistory = this.commandHistory.slice(0, 100);
    }
    
    this.saveHistory();
  }

  private saveHistory(): void {
    try {
      const historyFile = join(this.projectPath, '.chater_history');
      const fs = require('fs');
      fs.writeFileSync(historyFile, JSON.stringify(this.commandHistory, null, 2));
    } catch (error) {
      // 静默失败
    }
  }

  public getHistory(): string[] {
    return [...this.commandHistory];
  }
}