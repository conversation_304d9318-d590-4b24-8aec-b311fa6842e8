import chalk from 'chalk';
import { globSync } from 'glob';
import { existsSync, statSync } from 'fs';
import { resolve } from 'path';

export interface ShortcutResult {
  type: 'command' | 'bash' | 'filepath' | 'memory' | 'chat';
  content: string;
  original: string;
  metadata?: any;
}

export class ShortcutParser {
  private projectPath: string;

  constructor(projectPath: string = process.cwd()) {
    this.projectPath = projectPath;
  }

  /**
   * 解析输入中的快捷键
   * / - 内置命令
   * ! - bash 命令
   * @ - 文件路径
   * # - 记忆操作
   */
  public parseInput(input: string): ShortcutResult {
    const trimmed = input.trim();
    
    if (!trimmed) {
      return {
        type: 'chat',
        content: trimmed,
        original: input
      };
    }

    // / 命令快捷键
    if (trimmed.startsWith('/')) {
      return this.parseCommand(trimmed);
    }

    // ! bash 快捷键
    if (trimmed.startsWith('!')) {
      return this.parseBash(trimmed);
    }

    // @ 文件路径快捷键
    if (trimmed.startsWith('@')) {
      return this.parseFilePath(trimmed);
    }

    // # 记忆快捷键
    if (trimmed.startsWith('#')) {
      return this.parseMemory(trimmed);
    }

    // 默认是聊天
    return {
      type: 'chat',
      content: trimmed,
      original: input
    };
  }

  private parseCommand(input: string): ShortcutResult {
    const content = input.slice(1).trim(); // 移除 /
    
    if (!content) {
      return {
        type: 'command',
        content: 'help',
        original: input,
        metadata: { shortcut: true }
      };
    }

    return {
      type: 'command',
      content,
      original: input,
      metadata: { shortcut: true }
    };
  }

  private parseBash(input: string): ShortcutResult {
    const content = input.slice(1).trim(); // 移除 !
    
    if (!content) {
      return {
        type: 'chat',
        content: '请输入要执行的 bash 命令，例如: !git status',
        original: input
      };
    }

    return {
      type: 'bash',
      content,
      original: input,
      metadata: { shortcut: true }
    };
  }

  private parseFilePath(input: string): ShortcutResult {
    const pathQuery = input.slice(1).trim(); // 移除 @
    
    if (!pathQuery) {
      return {
        type: 'chat',
        content: '请输入文件路径或模式，例如: @src/*.ts 或 @package.json',
        original: input
      };
    }

    // 检查是否是完整路径
    const fullPath = resolve(this.projectPath, pathQuery);
    if (existsSync(fullPath)) {
      const stat = statSync(fullPath);
      const fileInfo = {
        path: fullPath,
        relativePath: pathQuery,
        isFile: stat.isFile(),
        isDirectory: stat.isDirectory(),
        size: stat.size,
        modified: stat.mtime
      };

      return {
        type: 'filepath',
        content: pathQuery,
        original: input,
        metadata: { 
          shortcut: true,
          fileInfo,
          found: true
        }
      };
    }

    // 尝试 glob 模式匹配
    try {
      const matches = globSync(pathQuery, { 
        cwd: this.projectPath,
        nodir: false
      });

      return {
        type: 'filepath',
        content: pathQuery,
        original: input,
        metadata: { 
          shortcut: true,
          matches: matches.slice(0, 10), // 限制显示数量
          totalMatches: matches.length,
          isGlob: true
        }
      };
    } catch (error) {
      return {
        type: 'chat',
        content: `文件路径查询错误: ${pathQuery}`,
        original: input,
        metadata: { error: error instanceof Error ? error.message : '未知错误' }
      };
    }
  }

  private parseMemory(input: string): ShortcutResult {
    const content = input.slice(1).trim(); // 移除 #
    
    if (!content) {
      return {
        type: 'memory',
        content: 'view',
        original: input,
        metadata: { shortcut: true, action: 'view' }
      };
    }

    // 检查是否是特定的记忆命令
    const memoryCommands = ['view', 'clear', 'search', 'insight', 'todo'];
    const parts = content.split(' ');
    const action = parts[0].toLowerCase();

    if (memoryCommands.includes(action)) {
      return {
        type: 'memory',
        content,
        original: input,
        metadata: { 
          shortcut: true, 
          action,
          args: parts.slice(1)
        }
      };
    }

    // 默认是添加洞察
    return {
      type: 'memory',
      content: `insight ${content}`,
      original: input,
      metadata: { 
        shortcut: true, 
        action: 'insight',
        args: [content]
      }
    };
  }

  /**
   * 生成快捷键帮助信息
   */
  public getShortcutHelp(): string {
    return chalk.blue('\n🚀 快捷键帮助\n') +
      chalk.yellow('命令快捷键:') + '\n' +
      '  /help          - 显示帮助 (等同于 help 命令)\n' +
      '  /exit          - 优雅退出 chater (推荐)\n' +
      '  /config        - 配置设置\n' +
      '  /models        - 查看模型\n' +
      '  /memory        - 查看记忆\n' +
      '  /              - 显示帮助\n\n' +
      
      chalk.yellow('系统命令快捷键:') + '\n' +
      '  !git status    - 执行 git status\n' +
      '  !npm install   - 执行 npm install\n' +
      '  !ls -la        - 执行 ls -la\n' +
      '  !              - 显示 bash 帮助\n\n' +
      
      chalk.yellow('文件路径快捷键:') + '\n' +
      '  @package.json  - 查看文件信息\n' +
      '  @package.json 分析依赖 - AI 分析文件\n' +
      '  @src/*.ts      - 匹配 TypeScript 文件\n' +
      '  @dist/         - 查看目录信息\n' +
      '  @              - 显示文件帮助\n\n' +
      
      chalk.yellow('记忆操作快捷键:') + '\n' +
      '  #view          - 查看项目记忆\n' +
      '  #重要发现      - 添加洞察 (默认动作)\n' +
      '  #todo 任务     - 添加待办事项\n' +
      '  #search 关键词 - 搜索记忆\n' +
      '  #              - 查看记忆状态\n\n' +
      
      chalk.red('⚠️  注意: Ctrl+C 会强制退出程序，建议使用 /exit 优雅退出\n') +
      chalk.gray('💡 提示: 直接输入问题即可与 AI 对话');
  }
}