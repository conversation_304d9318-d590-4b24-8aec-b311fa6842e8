import { EventEmitter } from 'events';
import { spawn, ChildProcess } from 'child_process';
import {
  MCPServerConfig,
  MCPClientInfo,
  MCPClientStatus,
  MCPEvent,
  MCPEventType,
  MCPManagerConfig,
  MCPToolWrapper,
  MCPConnectionStats,
  Tool,
  Resource,
  Prompt,
  CallToolRequest,
  CallToolResult,
  ListToolsRequest,
  ListToolsResult,
  ListResourcesRequest,
  ListResourcesResult,
  ListPromptsRequest,
  ListPromptsResult,
  InitializeRequest,
  InitializeResult,
  JSONRPCRequest,
  JSONRPCResponse,
  JSONRPCError,
  ClientCapabilities,
  ServerCapabilities
} from '../types/mcp';

/**
 * MCP 客户端管理器
 * 负责管理多个 MCP 服务器连接的生命周期
 */
export class MCPManager extends EventEmitter {
  private clients: Map<string, MCPClient> = new Map();
  private config: MCPManagerConfig;
  private healthCheckTimer?: NodeJS.Timeout;

  constructor(config: MCPManagerConfig) {
    super();
    this.config = config;
    this.startHealthCheck();
  }

  /**
   * 添加 MCP 服务器连接
   */
  async addServer(serverConfig: MCPServerConfig): Promise<void> {
    if (this.clients.has(serverConfig.id)) {
      throw new Error(`MCP server with id '${serverConfig.id}' already exists`);
    }

    if (this.clients.size >= this.config.maxConcurrentConnections) {
      throw new Error(`Maximum concurrent connections (${this.config.maxConcurrentConnections}) reached`);
    }

    const client = new MCPClient(serverConfig, this.config);
    
    // 监听客户端事件
    client.on('connected', () => this.emitEvent('client_connected', serverConfig.id));
    client.on('disconnected', () => this.emitEvent('client_disconnected', serverConfig.id));
    client.on('error', (error) => this.emitEvent('client_error', serverConfig.id, { error }));
    client.on('tool_called', (data) => this.emitEvent('tool_called', serverConfig.id, data));

    this.clients.set(serverConfig.id, client);

    if (serverConfig.enabled) {
      await client.connect();
    }
  }

  /**
   * 移除 MCP 服务器连接
   */
  async removeServer(serverId: string): Promise<void> {
    const client = this.clients.get(serverId);
    if (!client) {
      throw new Error(`MCP server with id '${serverId}' not found`);
    }

    await client.disconnect();
    this.clients.delete(serverId);
  }

  /**
   * 获取所有客户端信息
   */
  getClients(): MCPClientInfo[] {
    return Array.from(this.clients.values()).map(client => client.getInfo());
  }

  /**
   * 获取特定客户端信息
   */
  getClient(serverId: string): MCPClientInfo | undefined {
    const client = this.clients.get(serverId);
    return client?.getInfo();
  }

  /**
   * 获取所有可用工具
   */
  getAllTools(): MCPToolWrapper[] {
    const tools: MCPToolWrapper[] = [];
    
    for (const client of this.clients.values()) {
      const info = client.getInfo();
      if (info.status === 'connected') {
        for (const tool of info.tools) {
          tools.push({
            tool,
            serverId: info.config.id,
            serverName: info.config.name,
            call: (args) => client.callTool(tool.name, args)
          });
        }
      }
    }
    
    return tools;
  }

  /**
   * 调用工具
   */
  async callTool(serverId: string, toolName: string, args: Record<string, any>): Promise<CallToolResult> {
    const client = this.clients.get(serverId);
    if (!client) {
      throw new Error(`MCP server with id '${serverId}' not found`);
    }

    return await client.callTool(toolName, args);
  }

  /**
   * 连接所有启用的服务器
   */
  async connectAll(): Promise<void> {
    const promises = Array.from(this.clients.values())
      .filter(client => client.getInfo().config.enabled)
      .map(client => client.connect().catch(err => 
        console.warn(`Failed to connect to MCP server: ${err.message}`)
      ));
    
    await Promise.all(promises);
  }

  /**
   * 断开所有连接
   */
  async disconnectAll(): Promise<void> {
    const promises = Array.from(this.clients.values())
      .map(client => client.disconnect());
    
    await Promise.all(promises);
    
    if (this.healthCheckTimer) {
      clearInterval(this.healthCheckTimer);
    }
  }

  /**
   * 重新连接特定服务器
   */
  async reconnectServer(serverId: string): Promise<void> {
    const client = this.clients.get(serverId);
    if (!client) {
      throw new Error(`MCP server with id '${serverId}' not found`);
    }

    await client.reconnect();
  }

  /**
   * 启动健康检查
   */
  private startHealthCheck(): void {
    if (this.config.healthCheckInterval > 0) {
      this.healthCheckTimer = setInterval(() => {
        this.performHealthCheck();
      }, this.config.healthCheckInterval);
    }
  }

  /**
   * 执行健康检查
   */
  private async performHealthCheck(): Promise<void> {
    for (const client of this.clients.values()) {
      const info = client.getInfo();
      if (info.config.enabled && info.status === 'error' && this.config.autoReconnect) {
        try {
          await client.reconnect();
        } catch (error) {
          console.warn(`Health check reconnection failed for ${info.config.id}: ${error}`);
        }
      }
    }
  }

  /**
   * 发出事件
   */
  private emitEvent(type: MCPEventType, serverId: string, data?: any): void {
    const event: MCPEvent = {
      type,
      serverId,
      timestamp: new Date(),
      data
    };
    this.emit('mcp_event', event);
  }
}

/**
 * MCP 客户端类
 * 管理单个 MCP 服务器连接
 */
class MCPClient extends EventEmitter {
  private config: MCPServerConfig;
  private managerConfig: MCPManagerConfig;
  private status: MCPClientStatus = 'disconnected';
  private process?: ChildProcess;
  private capabilities?: ServerCapabilities;
  private tools: Tool[] = [];
  private resources: Resource[] = [];
  private prompts: Prompt[] = [];
  private stats: MCPConnectionStats;
  private lastConnected?: Date;
  private error?: string;
  private requestId = 0;
  private pendingRequests = new Map<number, { resolve: Function; reject: Function }>();

  constructor(config: MCPServerConfig, managerConfig: MCPManagerConfig) {
    super();
    this.config = config;
    this.managerConfig = managerConfig;
    this.stats = {
      connectCount: 0,
      reconnectCount: 0,
      toolCallCount: 0,
      resourceReadCount: 0,
      promptGetCount: 0,
      errorCount: 0
    };
  }

  /**
   * 连接到 MCP 服务器
   */
  async connect(): Promise<void> {
    if (this.status === 'connected' || this.status === 'connecting') {
      return;
    }

    this.status = 'connecting';
    this.error = undefined;

    try {
      if (this.config.transport === 'stdio') {
        await this.connectStdio();
      } else {
        throw new Error(`Transport type '${this.config.transport}' not yet implemented`);
      }

      await this.initialize();
      await this.loadCapabilities();

      this.status = 'connected';
      this.lastConnected = new Date();
      this.stats.connectCount++;
      this.emit('connected');

    } catch (error) {
      this.status = 'error';
      this.error = error instanceof Error ? error.message : String(error);
      this.stats.errorCount++;
      this.emit('error', error);
      throw error;
    }
  }

  /**
   * 断开连接
   */
  async disconnect(): Promise<void> {
    if (this.status === 'disconnected') {
      return;
    }

    this.status = 'disconnected';
    
    if (this.process) {
      this.process.kill();
      this.process = undefined;
    }

    this.emit('disconnected');
  }

  /**
   * 重新连接
   */
  async reconnect(): Promise<void> {
    await this.disconnect();
    this.stats.reconnectCount++;
    await this.connect();
  }

  /**
   * 获取客户端信息
   */
  getInfo(): MCPClientInfo {
    return {
      config: this.config,
      status: this.status,
      capabilities: this.capabilities,
      tools: this.tools,
      resources: this.resources,
      prompts: this.prompts,
      lastConnected: this.lastConnected,
      error: this.error,
      stats: { ...this.stats }
    };
  }

  /**
   * 调用工具
   */
  async callTool(toolName: string, args: Record<string, any>): Promise<CallToolResult> {
    if (this.status !== 'connected') {
      throw new Error(`Cannot call tool: client is ${this.status}`);
    }

    const request: CallToolRequest = {
      method: 'tools/call',
      params: {
        name: toolName,
        arguments: args
      }
    };

    this.stats.toolCallCount++;
    this.emit('tool_called', { toolName, args });

    return await this.sendRequest(request);
  }

  /**
   * 建立 stdio 连接
   */
  private async connectStdio(): Promise<void> {
    if (this.config.connection.type !== 'stdio') {
      throw new Error('Invalid connection config for stdio transport');
    }

    const { command, args = [], cwd, env } = this.config.connection;
    
    this.process = spawn(command, args, {
      cwd: cwd || process.cwd(),
      env: { ...process.env, ...this.config.env, ...env },
      stdio: ['pipe', 'pipe', 'pipe']
    });

    this.process.on('error', (error) => {
      this.status = 'error';
      this.error = error.message;
      this.stats.errorCount++;
      this.emit('error', error);
    });

    this.process.on('exit', (code) => {
      if (this.status === 'connected') {
        this.status = 'disconnected';
        this.emit('disconnected');
      }
    });

    // 设置消息处理
    if (this.process.stdout) {
      this.process.stdout.on('data', (data) => {
        this.handleMessage(data.toString());
      });
    }
  }

  /**
   * 初始化连接
   */
  private async initialize(): Promise<void> {
    const capabilities: ClientCapabilities = {
      tools: { listChanged: true },
      resources: { subscribe: true, listChanged: true },
      prompts: { listChanged: true }
    };

    const request: InitializeRequest = {
      method: 'initialize',
      params: {
        protocolVersion: '2024-11-05',
        capabilities,
        clientInfo: {
          name: 'chater',
          version: '0.3.3'
        }
      }
    };

    const result: InitializeResult = await this.sendRequest(request);
    this.capabilities = result.capabilities;
  }

  /**
   * 加载服务器能力
   */
  private async loadCapabilities(): Promise<void> {
    // 加载工具列表
    if (this.capabilities?.tools) {
      try {
        const toolsResult: ListToolsResult = await this.sendRequest({ method: 'tools/list' });
        this.tools = toolsResult.tools;
      } catch (error) {
        console.warn(`Failed to load tools: ${error}`);
      }
    }

    // 加载资源列表
    if (this.capabilities?.resources) {
      try {
        const resourcesResult: ListResourcesResult = await this.sendRequest({ method: 'resources/list' });
        this.resources = resourcesResult.resources;
      } catch (error) {
        console.warn(`Failed to load resources: ${error}`);
      }
    }

    // 加载提示列表
    if (this.capabilities?.prompts) {
      try {
        const promptsResult: ListPromptsResult = await this.sendRequest({ method: 'prompts/list' });
        this.prompts = promptsResult.prompts;
      } catch (error) {
        console.warn(`Failed to load prompts: ${error}`);
      }
    }
  }

  /**
   * 发送请求
   */
  private async sendRequest(request: any): Promise<any> {
    return new Promise((resolve, reject) => {
      const id = ++this.requestId;
      const jsonrpcRequest = {
        jsonrpc: '2.0',
        id,
        ...request
      };

      this.pendingRequests.set(id, { resolve, reject });

      // 设置超时
      const timeout = setTimeout(() => {
        this.pendingRequests.delete(id);
        reject(new Error(`Request timeout after ${this.config.timeout || this.managerConfig.defaultTimeout}ms`));
      }, this.config.timeout || this.managerConfig.defaultTimeout);

      // 发送消息
      if (this.process?.stdin) {
        this.process.stdin.write(JSON.stringify(jsonrpcRequest) + '\n');
      } else {
        clearTimeout(timeout);
        this.pendingRequests.delete(id);
        reject(new Error('No active connection'));
      }

      // 清理超时
      this.pendingRequests.get(id)!.resolve = (result: any) => {
        clearTimeout(timeout);
        resolve(result);
      };
      this.pendingRequests.get(id)!.reject = (error: any) => {
        clearTimeout(timeout);
        reject(error);
      };
    });
  }

  /**
   * 处理收到的消息
   */
  private handleMessage(data: string): void {
    const lines = data.trim().split('\n');
    
    for (const line of lines) {
      if (!line.trim()) continue;
      
      try {
        const message = JSON.parse(line);
        
        if (message.id && this.pendingRequests.has(message.id)) {
          const { resolve, reject } = this.pendingRequests.get(message.id)!;
          this.pendingRequests.delete(message.id);
          
          if (message.error) {
            reject(new Error(message.error.message || 'Unknown error'));
          } else {
            resolve(message.result);
          }
        }
      } catch (error) {
        console.warn(`Failed to parse MCP message: ${error}`);
      }
    }
  }
}
