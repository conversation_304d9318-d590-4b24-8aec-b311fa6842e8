/**
 * 现代化 UI 组件导出
 * 统一导出所有 UI 相关组件
 */

export { ModernCLI } from './ModernCLI';
export { EnhancedInput, InputSuggestion, InputOptions } from './EnhancedInput';
export { IntelliSense, CompletionItem, CompletionKind } from './IntelliSense';
export { StatusBar, StatusInfo } from './StatusBar';
export { Animations, AnimationOptions, ThemeColors } from './Animations';

// 工厂模式和依赖注入
import { ModernCLI } from './ModernCLI';
import { InteractiveContext } from '../core/interactive';

/**
 * UI 工厂接口
 */
export interface UIFactory {
  createCLI(context: InteractiveContext): ModernCLI;
}

/**
 * 默认 UI 工厂实现
 */
export class DefaultUIFactory implements UIFactory {
  createCLI(context: InteractiveContext): ModernCLI {
    return new ModernCLI(context);
  }
}

/**
 * 创建现代化 CLI 实例（保持向后兼容）
 */
export function createModernCLI(context: InteractiveContext): ModernCLI {
  const factory = new DefaultUIFactory();
  return factory.createCLI(context);
}

/**
 * 启动现代化 CLI 界面
 */
export async function startModernInterface(
  context: InteractiveContext, 
  factory: UIFactory = new DefaultUIFactory()
): Promise<void> {
  const cli = factory.createCLI(context);
  
  // 设置退出处理
  process.on('SIGINT', () => {
    cli.stop();
    process.exit(0);
  });

  process.on('SIGTERM', () => {
    cli.stop();
    process.exit(0);
  });

  // 启动界面
  await cli.start();
}
