/**
 * 性能监控工具
 * 用于监控和分析应用性能
 */
export class PerformanceMonitor {
  private static measurements: Map<string, number[]> = new Map();
  private static warningThreshold = 100; // 100ms

  /**
   * 测量函数执行时间
   */
  static async measureTime<T>(name: string, fn: () => Promise<T>): Promise<T> {
    const start = performance.now();
    
    try {
      const result = await fn();
      const duration = performance.now() - start;
      
      this.recordMeasurement(name, duration);
      
      if (duration > this.warningThreshold) {
        console.warn(`⚠️ 操作 ${name} 耗时 ${duration.toFixed(2)}ms`);
      }
      
      return result;
    } catch (error) {
      const duration = performance.now() - start;
      this.recordMeasurement(name, duration);
      throw error;
    }
  }

  /**
   * 测量同步函数执行时间
   */
  static measureSync<T>(name: string, fn: () => T): T {
    const start = performance.now();
    
    try {
      const result = fn();
      const duration = performance.now() - start;
      
      this.recordMeasurement(name, duration);
      
      if (duration > this.warningThreshold) {
        console.warn(`⚠️ 操作 ${name} 耗时 ${duration.toFixed(2)}ms`);
      }
      
      return result;
    } catch (error) {
      const duration = performance.now() - start;
      this.recordMeasurement(name, duration);
      throw error;
    }
  }

  /**
   * 记录测量结果
   */
  private static recordMeasurement(name: string, duration: number): void {
    if (!this.measurements.has(name)) {
      this.measurements.set(name, []);
    }
    
    const measurements = this.measurements.get(name)!;
    measurements.push(duration);
    
    // 只保留最近100个测量结果
    if (measurements.length > 100) {
      measurements.shift();
    }
  }

  /**
   * 获取操作的统计信息
   */
  static getStats(name: string): {
    count: number;
    average: number;
    min: number;
    max: number;
  } | null {
    const measurements = this.measurements.get(name);
    if (!measurements || measurements.length === 0) {
      return null;
    }

    const count = measurements.length;
    const sum = measurements.reduce((a, b) => a + b, 0);
    const average = sum / count;
    const min = Math.min(...measurements);
    const max = Math.max(...measurements);

    return { count, average, min, max };
  }

  /**
   * 生成性能报告
   */
  static generateReport(): string {
    const report: string[] = ['📊 性能监控报告', ''];
    
    for (const [name, _measurements] of this.measurements.entries()) {
      const stats = this.getStats(name);
      if (stats) {
        report.push(
          `${name}:`,
          `  调用次数: ${stats.count}`,
          `  平均耗时: ${stats.average.toFixed(2)}ms`,
          `  最短耗时: ${stats.min.toFixed(2)}ms`,
          `  最长耗时: ${stats.max.toFixed(2)}ms`,
          ''
        );
      }
    }

    return report.join('\n');
  }

  /**
   * 清空测量数据
   */
  static clear(): void {
    this.measurements.clear();
  }

  /**
   * 设置警告阈值
   */
  static setWarningThreshold(ms: number): void {
    this.warningThreshold = ms;
  }
}