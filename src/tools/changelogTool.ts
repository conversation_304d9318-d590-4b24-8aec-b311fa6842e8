import { execSync, exec } from 'child_process';
import { existsSync, readFileSync, writeFileSync } from 'fs';
import { join } from 'path';
import { Tool } from '../types';

export const changelogTool: Tool = {
  name: 'changelog',
  description: '管理项目 CHANGELOG.md 文件，支持查看、添加和自动生成版本更新记录。遵循 Keep a Changelog 格式标准。',
  parameters: {
    type: 'object',
    properties: {
      action: {
        type: 'string',
        description: '要执行的操作',
        enum: ['read', 'add', 'generate', 'init', 'release']
      },
      version: {
        type: 'string',
        description: '版本号（用于 add 和 release 操作）'
      },
      type: {
        type: 'string',
        description: '变更类型',
        enum: ['added', 'changed', 'deprecated', 'removed', 'fixed', 'security']
      },
      description: {
        type: 'string',
        description: '变更描述'
      },
      projectPath: {
        type: 'string',
        description: '项目根目录路径（默认当前目录）'
      },
      autoGenerate: {
        type: 'boolean',
        description: '是否基于 git 提交自动生成（用于 generate 操作）'
      }
    },
    required: ['action']
  },
  execute: async (args: Record<string, any>) => {
    const { action, version, type, description, projectPath = process.cwd(), autoGenerate = true } = args;
    
    const changelogPath = join(projectPath, 'CHANGELOG.md');
    
    try {
      switch (action) {
        case 'read':
          return await readChangelog(changelogPath);
          
        case 'add':
          if (!version || !type || !description) {
            return {
              success: false,
              error: 'add 操作需要 version、type 和 description 参数'
            };
          }
          return await addChangelogEntry(changelogPath, version, type, description);
          
        case 'generate':
          return await generateChangelog(changelogPath, projectPath, version, autoGenerate);
          
        case 'init':
          return await initChangelog(changelogPath);
          
        case 'release':
          if (!version) {
            return {
              success: false,
              error: 'release 操作需要 version 参数'
            };
          }
          return await releaseVersion(changelogPath, version);
          
        default:
          return {
            success: false,
            error: `未知的操作: ${action}`
          };
      }
    } catch (error) {
      return {
        success: false,
        error: error instanceof Error ? error.message : 'CHANGELOG 操作失败'
      };
    }
  }
};

export const smartChangelogTool: Tool = {
  name: 'smart_changelog',
  description: '智能 CHANGELOG 管理：自动分析 git 历史，生成符合规范的版本更新记录。支持语义化版本控制。',
  parameters: {
    type: 'object',
    properties: {
      intent: {
        type: 'string',
        description: '操作意图',
        enum: ['update', 'release', 'preview', 'init']
      },
      targetVersion: {
        type: 'string',
        description: '目标版本号（可选，会自动推断）'
      },
      fromVersion: {
        type: 'string',
        description: '起始版本号（用于生成范围）'
      },
      includeUnreleased: {
        type: 'boolean',
        description: '是否包含未发布的变更'
      },
      projectPath: {
        type: 'string',
        description: '项目路径'
      }
    },
    required: ['intent']
  },
  execute: async (args: Record<string, any>) => {
    const { intent, targetVersion, fromVersion, includeUnreleased = true, projectPath = process.cwd() } = args;
    
    try {
      const changelogPath = join(projectPath, 'CHANGELOG.md');
      const packageJsonPath = join(projectPath, 'package.json');
      
      // 获取当前版本
      let currentVersion = '0.1.0';
      if (existsSync(packageJsonPath)) {
        const packageJson = JSON.parse(readFileSync(packageJsonPath, 'utf8'));
        currentVersion = packageJson.version;
      }
      
      switch (intent) {
        case 'init':
          return await initChangelog(changelogPath);
          
        case 'preview':
          return await previewChanges(projectPath, fromVersion || currentVersion, includeUnreleased);
          
        case 'update':
          const version = targetVersion || currentVersion;
          return await smartUpdate(changelogPath, projectPath, version, fromVersion);
          
        case 'release':
          const releaseVersion = targetVersion || currentVersion;
          return await smartRelease(changelogPath, projectPath, releaseVersion);
          
        default:
          return {
            success: false,
            error: `未知的意图: ${intent}`
          };
      }
    } catch (error) {
      return {
        success: false,
        error: error instanceof Error ? error.message : '智能 CHANGELOG 操作失败'
      };
    }
  }
};

// === 核心功能实现 ===

async function readChangelog(changelogPath: string) {
  if (!existsSync(changelogPath)) {
    return {
      success: false,
      error: 'CHANGELOG.md 文件不存在',
      path: changelogPath
    };
  }
  
  const content = readFileSync(changelogPath, 'utf8');
  const sections = parseChangelog(content);
  
  return {
    success: true,
    path: changelogPath,
    content,
    sections,
    latestVersion: sections.length > 0 ? sections[0].version : null
  };
}

async function addChangelogEntry(changelogPath: string, version: string, type: string, description: string) {
  let content = '';
  let sections: any[] = [];
  
  if (existsSync(changelogPath)) {
    content = readFileSync(changelogPath, 'utf8');
    sections = parseChangelog(content);
  } else {
    content = generateChangelogHeader();
  }
  
  // 查找或创建版本段落
  let versionSection = sections.find(s => s.version === version);
  if (!versionSection) {
    versionSection = {
      version,
      date: 'Unreleased',
      changes: {
        added: [],
        changed: [],
        deprecated: [],
        removed: [],
        fixed: [],
        security: []
      }
    };
    sections.unshift(versionSection);
  }
  
  // 添加变更条目
  if (!versionSection.changes[type]) {
    versionSection.changes[type] = [];
  }
  versionSection.changes[type].push(description);
  
  // 重新生成内容
  const newContent = generateChangelogContent(sections);
  writeFileSync(changelogPath, newContent, 'utf8');
  
  return {
    success: true,
    path: changelogPath,
    version,
    type,
    description,
    message: `已添加 ${type} 类型的变更到版本 ${version}`
  };
}

async function generateChangelog(changelogPath: string, projectPath: string, version?: string, autoGenerate = true) {
  if (!autoGenerate) {
    return await initChangelog(changelogPath);
  }
  
  try {
    // 获取 git 提交历史
    const gitLog = execSync('git log --oneline --pretty=format:"%h|%s|%ad" --date=short', {
      cwd: projectPath,
      encoding: 'utf8'
    });
    
    const commits = parseGitCommits(gitLog);
    const groupedChanges = groupCommitsByType(commits);
    
    // 生成或更新 changelog
    let content = '';
    let sections: any[] = [];
    
    if (existsSync(changelogPath)) {
      content = readFileSync(changelogPath, 'utf8');
      sections = parseChangelog(content);
    }
    
    // 创建新版本段落
    const newVersion = version || 'Unreleased';
    const newSection = {
      version: newVersion,
      date: version ? new Date().toISOString().split('T')[0] : 'Unreleased',
      changes: groupedChanges
    };
    
    // 如果已存在相同版本，替换；否则添加到顶部
    const existingIndex = sections.findIndex(s => s.version === newVersion);
    if (existingIndex >= 0) {
      sections[existingIndex] = newSection;
    } else {
      sections.unshift(newSection);
    }
    
    const newContent = generateChangelogContent(sections);
    writeFileSync(changelogPath, newContent, 'utf8');
    
    return {
      success: true,
      path: changelogPath,
      version: newVersion,
      commitsProcessed: commits.length,
      changes: groupedChanges,
      message: `已生成 ${newVersion} 版本的 CHANGELOG`
    };
  } catch (error) {
    return {
      success: false,
      error: `生成 CHANGELOG 失败: ${error instanceof Error ? error.message : '未知错误'}`
    };
  }
}

async function initChangelog(changelogPath: string) {
  if (existsSync(changelogPath)) {
    return {
      success: false,
      error: 'CHANGELOG.md 已存在',
      path: changelogPath
    };
  }
  
  const initialContent = generateChangelogHeader() + generateUnreleasedSection();
  writeFileSync(changelogPath, initialContent, 'utf8');
  
  return {
    success: true,
    path: changelogPath,
    message: '已初始化 CHANGELOG.md 文件'
  };
}

async function releaseVersion(changelogPath: string, version: string) {
  if (!existsSync(changelogPath)) {
    return {
      success: false,
      error: 'CHANGELOG.md 文件不存在'
    };
  }
  
  const content = readFileSync(changelogPath, 'utf8');
  const sections = parseChangelog(content);
  
  // 查找未发布的版本或指定版本
  const versionSection = sections.find(s => s.version === version || s.version === 'Unreleased');
  if (!versionSection) {
    return {
      success: false,
      error: `未找到版本 ${version} 的变更记录`
    };
  }
  
  // 更新版本信息
  versionSection.version = version;
  versionSection.date = new Date().toISOString().split('T')[0];
  
  const newContent = generateChangelogContent(sections);
  writeFileSync(changelogPath, newContent, 'utf8');
  
  return {
    success: true,
    path: changelogPath,
    version,
    releaseDate: versionSection.date,
    message: `已发布版本 ${version}`
  };
}

// === 辅助功能 ===

async function previewChanges(projectPath: string, fromVersion: string, includeUnreleased: boolean) {
  try {
    let gitCommand = 'git log --oneline --pretty=format:"%h|%s|%ad" --date=short';
    
    // 如果指定了起始版本，添加范围限制
    if (fromVersion && fromVersion !== 'Unreleased') {
      try {
        execSync(`git rev-parse ${fromVersion}`, { cwd: projectPath, stdio: 'ignore' });
        gitCommand += ` ${fromVersion}..HEAD`;
      } catch {
        // 版本标签不存在，使用所有提交
      }
    }
    
    const gitLog = execSync(gitCommand, {
      cwd: projectPath,
      encoding: 'utf8'
    });
    
    const commits = parseGitCommits(gitLog);
    const groupedChanges = groupCommitsByType(commits);
    
    return {
      success: true,
      fromVersion,
      commitsCount: commits.length,
      changes: groupedChanges,
      preview: true,
      message: `预览了 ${commits.length} 个提交的变更`
    };
  } catch (error) {
    return {
      success: false,
      error: `预览变更失败: ${error instanceof Error ? error.message : '未知错误'}`
    };
  }
}

async function smartUpdate(changelogPath: string, projectPath: string, version: string, fromVersion?: string) {
  const previewResult = await previewChanges(projectPath, fromVersion || version, true);
  
  if (!previewResult.success) {
    return previewResult;
  }
  
  return await generateChangelog(changelogPath, projectPath, version, true);
}

async function smartRelease(changelogPath: string, projectPath: string, version: string) {
  // 先更新 changelog
  const updateResult = await smartUpdate(changelogPath, projectPath, version);
  if (!updateResult.success) {
    return updateResult;
  }
  
  // 然后标记为发布
  return await releaseVersion(changelogPath, version);
}

function parseChangelog(content: string) {
  const sections = [];
  const lines = content.split('\n');
  let currentSection: any = null;
  let currentType: string | null = null;
  
  for (const line of lines) {
    // 版本标题
    const versionMatch = line.match(/^## \[?([^\]]+)\]?(?:\s*-\s*(.+))?/);
    if (versionMatch) {
      if (currentSection) {
        sections.push(currentSection);
      }
      currentSection = {
        version: versionMatch[1],
        date: versionMatch[2] || 'Unreleased',
        changes: {
          added: [],
          changed: [],
          deprecated: [],
          removed: [],
          fixed: [],
          security: []
        }
      };
      currentType = null;
      continue;
    }
    
    // 变更类型标题
    const typeMatch = line.match(/^### (Added|Changed|Deprecated|Removed|Fixed|Security)/i);
    if (typeMatch && currentSection) {
      currentType = typeMatch[1].toLowerCase();
      continue;
    }
    
    // 变更条目
    const itemMatch = line.match(/^- (.+)/);
    if (itemMatch && currentSection && currentType) {
      if (!currentSection.changes[currentType]) {
        currentSection.changes[currentType] = [];
      }
      currentSection.changes[currentType].push(itemMatch[1]);
    }
  }
  
  if (currentSection) {
    sections.push(currentSection);
  }
  
  return sections;
}

function parseGitCommits(gitLog: string) {
  return gitLog.split('\n')
    .filter(line => line.trim())
    .map(line => {
      const [hash, message, date] = line.split('|');
      return { hash, message, date };
    });
}

function groupCommitsByType(commits: any[]) {
  const groups = {
    added: [] as string[],
    changed: [] as string[],
    deprecated: [] as string[],
    removed: [] as string[],
    fixed: [] as string[],
    security: [] as string[]
  };
  
  for (const commit of commits) {
    const message = commit.message.toLowerCase();
    
    if (message.startsWith('feat')) {
      groups.added.push(commit.message);
    } else if (message.startsWith('fix')) {
      groups.fixed.push(commit.message);
    } else if (message.startsWith('refactor') || message.startsWith('perf')) {
      groups.changed.push(commit.message);
    } else if (message.startsWith('security') || message.includes('security')) {
      groups.security.push(commit.message);
    } else if (message.startsWith('remove') || message.startsWith('delete')) {
      groups.removed.push(commit.message);
    } else if (message.startsWith('deprecate')) {
      groups.deprecated.push(commit.message);
    } else {
      // 默认归类为 changed
      groups.changed.push(commit.message);
    }
  }
  
  return groups;
}

function generateChangelogHeader() {
  return `# Changelog

All notable changes to this project will be documented in this file.

The format is based on [Keep a Changelog](https://keepachangelog.com/en/1.0.0/),
and this project adheres to [Semantic Versioning](https://semver.org/spec/v2.0.0.html).

`;
}

function generateUnreleasedSection() {
  return `## [Unreleased]

### Added
- Initial release

`;
}

function generateChangelogContent(sections: any[]) {
  let content = generateChangelogHeader();
  
  for (const section of sections) {
    content += `## [${section.version}]`;
    if (section.date && section.date !== 'Unreleased') {
      content += ` - ${section.date}`;
    }
    content += '\n\n';
    
    const types = ['added', 'changed', 'deprecated', 'removed', 'fixed', 'security'];
    for (const type of types) {
      const changes = section.changes[type] || [];
      if (changes.length > 0) {
        content += `### ${type.charAt(0).toUpperCase() + type.slice(1)}\n`;
        for (const change of changes) {
          content += `- ${change}\n`;
        }
        content += '\n';
      }
    }
  }
  
  return content;
}