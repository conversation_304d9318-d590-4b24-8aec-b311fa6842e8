import chalk from 'chalk';

export class SuggestionDisplay {
  /**
   * 显示快捷键建议面板
   */
  public static showQuickHelp(): void {
    console.log(chalk.blue('\n🚀 快捷键面板'));
    console.log(chalk.gray('┌─────────────────────────────────────────────┐'));
    console.log(chalk.gray('│ ') + chalk.blue('/') + chalk.white('help    ') + chalk.gray('│ ') + chalk.blue('!') + chalk.white('git status') + chalk.gray('  │ ') + chalk.blue('@') + chalk.white('src/    ') + chalk.gray(' │'));
    console.log(chalk.gray('│ ') + chalk.blue('/') + chalk.white('config  ') + chalk.gray('│ ') + chalk.blue('!') + chalk.white('npm build ') + chalk.gray('  │ ') + chalk.blue('@') + chalk.white('*.ts    ') + chalk.gray(' │'));
    console.log(chalk.gray('│ ') + chalk.blue('/') + chalk.white('models  ') + chalk.gray('│ ') + chalk.blue('!') + chalk.white('ls -la    ') + chalk.gray('  │ ') + chalk.blue('#') + chalk.white('重要发现') + chalk.gray(' │'));
    console.log(chalk.gray('│ ') + chalk.blue('/') + chalk.white('memory  ') + chalk.gray('│ ') + chalk.blue('!') + chalk.white('docker ps ') + chalk.gray('  │ ') + chalk.blue('#') + chalk.white('todo    ') + chalk.gray(' │'));
    console.log(chalk.gray('└─────────────────────────────────────────────┘'));
    console.log();
  }

  /**
   * 显示建议列表
   */
  public static showSuggestions(input: string, suggestions: string[]): void {
    if (suggestions.length === 0) return;

    const maxSuggestions = 5;
    const displaySuggestions = suggestions.slice(0, maxSuggestions);

    console.log(chalk.cyan(`\n💡 输入 "${input}" 的建议:`));
    displaySuggestions.forEach((suggestion, index) => {
      const number = chalk.gray(`${index + 1}.`);
      const content = this.formatSuggestion(suggestion);
      console.log(`  ${number} ${content}`);
    });

    if (suggestions.length > maxSuggestions) {
      console.log(chalk.gray(`  ... 还有 ${suggestions.length - maxSuggestions} 个建议`));
    }
    console.log();
  }

  /**
   * 格式化建议内容
   */
  private static formatSuggestion(suggestion: string): string {
    if (suggestion.startsWith('/')) {
      return chalk.blue('/') + chalk.white(suggestion.slice(1));
    }
    if (suggestion.startsWith('!')) {
      return chalk.green('!') + chalk.white(suggestion.slice(1));
    }
    if (suggestion.startsWith('@')) {
      return chalk.yellow('@') + chalk.white(suggestion.slice(1));
    }
    if (suggestion.startsWith('#')) {
      return chalk.magenta('#') + chalk.white(suggestion.slice(1));
    }
    return chalk.white(suggestion);
  }

  /**
   * 显示输入提示
   */
  public static showInputHint(): void {
    const hints = [
      chalk.blue('/') + chalk.gray('命令'),
      chalk.green('!') + chalk.gray('bash'),
      chalk.yellow('@') + chalk.gray('文件'),
      chalk.magenta('#') + chalk.gray('记忆')
    ];
    console.log(chalk.gray(`💡 ${hints.join(' | ')} | `) + chalk.gray('输入 ? 显示快捷键面板'));
  }

  /**
   * 显示实时计数器
   */
  public static showLiveCounter(input: string, suggestionCount: number): string {
    if (!input.trim()) return '';
    
    if (suggestionCount > 0) {
      return chalk.gray(` (${suggestionCount} 个建议)`);
    }
    
    return '';
  }

  /**
   * 显示快捷提示
   */
  public static showQuickTips(): void {
    const tips = [
      '💡 输入 ? 显示快捷键面板',
      '📝 输入 history 查看命令历史',
      '🔍 使用 Tab 键可以快速补全',
      '↑↓ 箭头键浏览历史命令'
    ];
    
    const randomTip = tips[Math.floor(Math.random() * tips.length)];
    console.log(chalk.gray(randomTip));
  }
}